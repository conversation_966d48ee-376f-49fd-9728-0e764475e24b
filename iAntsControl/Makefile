TARGET := iphone:clang:latest:14.0
INSTALL_TARGET_PROCESSES = SpringBoard


include $(THEOS)/makefiles/common.mk

TWEAK_NAME = iAntsControl

$(TWEAK_NAME)_FILES = $(shell find Sources/iAntsControl -name '*.swift') $(shell find Sources/iAntsControlC -name '*.m' -name '*.xm' -o -name '*.c' -o -name '*.mm' -o -name '*.cpp')
$(TWEAK_NAME)_SWIFTFLAGS = -ISources/iAntsControlC/include
$(TWEAK_NAME)_CFLAGS = -fobjc-arc -ISources/iAntsControlC/include

include $(THEOS_MAKE_PATH)/tweak.mk
