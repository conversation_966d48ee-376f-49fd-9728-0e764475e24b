#include "TouchIndicatorWindow.h"
#include "TouchIndicatorViewList.h"
#include "../handlers/Screen.h"
#include "../handlers/AlertBox.h"
#include "../Common.h"

#import "TouchIndicatorView.h"
#import "TouchIndicatorCoordinateView.h"

#include "IOHIDEvent.h"
#include "IOHIDEventData.h"
#include "IOHIDEventTypes.h"
#include "IOHIDEventSystemClient.h"
#include "IOHIDEventSystem.h"
#import <mach/mach.h>
#import <objc/runtime.h>

#define HIDE 0
#define SHOW 1
#define RELOAD 2

//#define COORDINATE_VIEW_WIDTH 100
#define COORDINATE_VIEW_HEIGHT 20


static Boolean isShowing = false;

static void IOHIDEventCallbackForTouchIndicator(void* target, void* refcon, IOHIDServiceRef service, IOHIDEventRef parentEvent);

static IOHIDEventSystemClientRef ioHIDEventSystemClient = NULL;
static CFRunLoopRef runLoopRef = NULL;



static CGFloat screenBoundsWidth = 0;
static CGFloat screenBoundsHeight = 0;
static CGFloat scale = 0;

static TouchIndicatorWindow *touchIndicatorWindow;

void report_memory(void) {
  struct task_basic_info info;
  mach_msg_type_number_t size = TASK_BASIC_INFO_COUNT;
  kern_return_t kerr = task_info(mach_task_self(),
                                 TASK_BASIC_INFO,
                                 (task_info_t)&info,
                                 &size);
  if( kerr == KERN_SUCCESS ) {
    iAntsControlAbilityLog(@"Memory in use (in bytes): %lu", info.resident_size);
    iAntsControlAbilityLog(@"Memory in use (in MiB): %f", ((CGFloat)info.resident_size / 1048576));
  } else {
    iAntsControlAbilityLog(@"Error with task_info(): %s", mach_error_string(kerr));
  }
}


void handleTouchIndicatorTaskWithRawData(UInt8* eventData, NSError **error)
{
    iAntsControlAbilityLog(@"触摸显示器");
    if ([[NSString stringWithFormat:@"%s", eventData] intValue] == HIDE)
    {
        iAntsControlAbilityLog(@"停止触摸显示器");
        stopTouchIndicator(error);
    }
    else if ([[NSString stringWithFormat:@"%s", eventData] intValue] == SHOW)
    {
        iAntsControlAbilityLog(@"启动触摸显示器");
        startTouchIndicator(error);
    }
    else
    {
        iAntsControlAbilityLog(@"Unknown touch indicator data");
        *error = [NSError errorWithDomain:@"com.data.iantscore.plugin.touchability" code:999 userInfo:@{NSLocalizedDescriptionKey:@"-1;;Unknown touch indicator data\r\n"}];
        return;
    }

}

void stopTouchIndicator(NSError **error)
{
    iAntsControlAbilityLog(@"Touch indicator turn off request");
    // set touch indicator window to nil
    touchIndicatorWindow = nil;
    // unregister callback
    if (ioHIDEventSystemClient && runLoopRef)
    {
        IOHIDEventSystemClientUnregisterEventCallback(ioHIDEventSystemClient);
        IOHIDEventSystemClientUnscheduleWithRunLoop(ioHIDEventSystemClient, runLoopRef, kCFRunLoopDefaultMode);

        ioHIDEventSystemClient = NULL;

        CFRunLoopStop(runLoopRef);
        runLoopRef = NULL;
    }

    isShowing = false;
}



void startTouchIndicator(NSError **error)
{
    if (isShowing)
    {
        // *error = [NSError errorWithDomain:@"com.data.iantscore.plugin.touchability" code:999 userInfo:@{NSLocalizedDescriptionKey:@"-1;;Touch indicator is already showing\r\n"}];
        // showAlertBox(@"### Error", @"Touch indicator is already showing", 999);
        return;
    }
    dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
        iAntsControlAbilityLog(@"Touch indicator turn on request");

        // check whether config file exist
        NSString *configFilePath = getCommonConfigFilePath();
        iAntsControlAbilityLog(@"configFilePath: %@", configFilePath);

        CGFloat red = 255;
        CGFloat green = 0;
        CGFloat blue = 0;
        CGFloat alpha = 0.7f;

        // get screen size
        CGRect bounds = [Screen getBounds];
        scale = [Screen getScale];
        screenBoundsWidth = CGRectGetWidth(bounds);
        screenBoundsHeight = CGRectGetHeight(bounds);
        iAntsControlAbilityLog(@"tap point: (%f, %f)", screenBoundsWidth, screenBoundsHeight);

        if (screenBoundsWidth > screenBoundsHeight)
            swapCGFloat(&screenBoundsWidth, &screenBoundsHeight);

        if (screenBoundsWidth == 0 || screenBoundsHeight == 0)
        {
            showAlertBox(@"Error", @"Cannot get screen bound.", 999);
            *error = [NSError errorWithDomain:@"com.data.iantscore.plugin.touchability" code:999 userInfo:@{NSLocalizedDescriptionKey:@"-1;;Cannot get screen bound\r\n"}];
            return;
        }

        // init a touch indicator window
        
        touchIndicatorWindow = [[TouchIndicatorWindow alloc] init];

        // create callback
        ioHIDEventSystemClient = IOHIDEventSystemClientCreate(kCFAllocatorDefault);
        

        IOHIDEventSystemClientScheduleWithRunLoop(ioHIDEventSystemClient, CFRunLoopGetCurrent(), kCFRunLoopDefaultMode);
        IOHIDEventSystemClientRegisterEventCallback(ioHIDEventSystemClient, (IOHIDEventSystemClientEventCallback)IOHIDEventCallbackForTouchIndicator, NULL, NULL);
        
        isShowing = true;

        runLoopRef = CFRunLoopGetCurrent();

        CFRunLoopRun();
        
    });
}

static void IOHIDEventCallbackForTouchIndicator(void* target, void* refcon, IOHIDServiceRef service, IOHIDEventRef parentEvent) 
{

    // iAntsControlAbilityLog(@"处理实时手势");
    if (IOHIDEventGetType(parentEvent) == kIOHIDEventTypeDigitizer){
        if (!touchIndicatorWindow)
        {
            iAntsControlAbilityLog(@"touchIndicatorWindow 不存在");
            return;
        }

        
        if (IOHIDEventGetType(parentEvent) == kIOHIDEventTypeDigitizer) {
            NSArray *childrens = (__bridge NSArray *)IOHIDEventGetChildren(parentEvent);

            for (int i = 0; i < [childrens count]; i++)
            {
                Boolean print = false;
                IOHIDEventRef event = (__bridge IOHIDEventRef)childrens[i];
                IOHIDFloat x = IOHIDEventGetFloatValue(event, (IOHIDEventField)kIOHIDEventFieldDigitizerX);
                IOHIDFloat y = IOHIDEventGetFloatValue(event, (IOHIDEventField)kIOHIDEventFieldDigitizerY);
                int eventMask = IOHIDEventGetIntegerValue(event, (IOHIDEventField)kIOHIDEventFieldDigitizerEventMask);
                int range = IOHIDEventGetIntegerValue(event, (IOHIDEventField)kIOHIDEventFieldDigitizerRange);
                int touch = IOHIDEventGetIntegerValue(event, (IOHIDEventField)kIOHIDEventFieldDigitizerTouch);
                int index = IOHIDEventGetIntegerValue(event, (IOHIDEventField)kIOHIDEventFieldDigitizerIndex);
                // iAntsControlAbilityLog(@"x %f : y %f. eventMask: %d. index: %d, range: %d. Touch: %d", x, y, eventMask, index, range, touch);
                // iAntsControlAbilityLog(@" x %f : y %f. eventMask: %d. index: %d, range: %d. Touch: %d.", x, y, eventMask, index, range, touch);

                IOHIDFloat majorRadius = IOHIDEventGetFloatValue(event, 0xb0014);

                CGFloat xOnScreen = x * screenBoundsWidth;
                CGFloat yOnScreen = y * screenBoundsHeight;

                if ( touch == 1 && eventMask & 2 ) {
                    // touch down
                    // iAntsControlAbilityLog(@"touch down 显示指示器");
                    [touchIndicatorWindow showIndicator:index withX:xOnScreen andY:yOnScreen majorRadius:majorRadius];
                }
                else if ( touch == 1 && eventMask & 4 ) {
                    // touch move
                    // iAntsControlAbilityLog(@"touch move 移动指示器");
                    [touchIndicatorWindow moveIndicator:index x:xOnScreen y:yOnScreen majorRadius:majorRadius];
                }
                else if (!touch && (eventMask & 2) ) {
                    // touch up
                    // iAntsControlAbilityLog(@"touch up 隐藏指示器");
                    [touchIndicatorWindow hideIndicator:index];
                }
            }
        }
    }
    
}


@implementation TouchIndicatorWindow
{
    //TouchIndicatorViewList* indicatorViewList;
    TouchIndicatorView* touchIndicatorViewList[20];
    TouchIndicatorCoordinateView* coordinateView[20];
    UIColor* indicatorColor;
}


- (id)init {
    self = [super init];
    if (self)
    {
        dispatch_async(dispatch_get_main_queue(), ^{
            indicatorColor = [UIColor colorWithRed:255 green:0 blue:0 alpha:0.5];
        });
    }
    return self;
}


- (void)hideIndicator:(int)index {
    if (index >= 20)
    {
        return;
    }
    dispatch_async(dispatch_get_main_queue(), ^{
        [touchIndicatorViewList[index-1] removeFromSuperview];
        touchIndicatorViewList[index-1] = nil;

        [coordinateView[index-1] removeFromSuperview];
        coordinateView[index-1] = nil;
    });
}

- (void)showIndicator:(int)index withX:(int)x andY:(int)y majorRadius:(CGFloat)radius {    
    iAntsControlAbilityLog(@"showIndicator %d %d %d %f", index, x, y, radius);
    if (index >= 20)
    {
        return;
    }
    if (touchIndicatorViewList[index-1] != nil)
    {
        iAntsControlAbilityLog(@"hideIndicator");
        [self hideIndicator:index];
    }
    dispatch_async(dispatch_get_main_queue(), ^{
        CGFloat indicatorSize = radius*SIZE_INDIACTOR_TOUCH_RADIUS_RATIO;
        // init a indicator
        CGFloat halfSize = indicatorSize/2;
        TouchIndicatorView *indicator = [[TouchIndicatorView alloc] initWithFrame:CGRectMake(x - halfSize, y - halfSize, indicatorSize, indicatorSize)];
        // iAntsControlAbilityLog(@"indicator:%@", indicator);
        indicator.layer.cornerRadius = halfSize;
        indicator.backgroundColor = indicatorColor;
        indicator.userInteractionEnabled = NO;
        // create touch coordinate view
        NSString *coordinateText = [NSString stringWithFormat:@"(%d, %d)", (int)(x * scale), (int)(y * scale)];
        // iAntsControlAbilityLog(@"point:(%d, %d)", (int)(x*scale), (int)(y*scale));
        UIFont *font = [UIFont fontWithName: @"Trebuchet MS" size: 11.0f];
        CGSize stringSize = [coordinateText sizeWithFont:font]; 
        CGFloat stringWidth = stringSize.width;

        TouchIndicatorCoordinateView *coordinateLabelView = [[TouchIndicatorCoordinateView alloc] initWithFrame:CGRectMake(x + halfSize + 5, y, stringWidth+5, COORDINATE_VIEW_HEIGHT)];
        coordinateLabelView.backgroundColor = indicatorColor;


        UILabel *coordinateLabel = [[UILabel alloc] initWithFrame:CGRectMake(0, 0, stringWidth+5, COORDINATE_VIEW_HEIGHT)];
        coordinateLabel.text = coordinateText;
        [coordinateLabel setTextColor:[UIColor whiteColor]];
        [coordinateLabel setBackgroundColor:[UIColor clearColor]];
        [coordinateLabel setFont:font]; 
        [coordinateLabelView addSubview:coordinateLabel];
        // iAntsControlAbilityLog(@"coordinateLabelView: %@", coordinateLabelView);
        coordinateLabelView.coordinateLabel = coordinateLabel;
        coordinateLabelView.userInteractionEnabled = NO;

        // add to list
        touchIndicatorViewList[index-1] = indicator;
        coordinateView[index-1] = coordinateLabelView;
        // iAntsControlAbilityLog(@"touchIndicatorViewList: %d %@", index, indicator);
        // iAntsControlAbilityLog(@"coordinateView: %d %@", index, coordinateLabelView);

        // add to subview
        UIWindow *keyWindow = [UIApplication sharedApplication].keyWindow;
        [keyWindow addSubview:indicator];
        [keyWindow addSubview:coordinateLabelView];
    });
}

- (void)setIndicatorColorWithRed:(CGFloat)red green:(CGFloat)green blue:(CGFloat)blue alpha:(CGFloat)alpha {
    indicatorColor = [UIColor colorWithRed:red/255 green:green/255 blue:blue/255 alpha:alpha];
}


- (void)moveIndicator:(int)index x:(CGFloat)x y:(CGFloat)y majorRadius:(CGFloat)radius {
    if (index >= 20)
    {
        return;
    }
    dispatch_async(dispatch_get_main_queue(), ^{
        if (touchIndicatorViewList[index-1] == NULL)
            return;

        // update width and height and cornerRadius
        CGFloat indicatorSize = radius*SIZE_INDIACTOR_TOUCH_RADIUS_RATIO;
        CGFloat halfSize = indicatorSize/2;
        touchIndicatorViewList[index-1].frame = CGRectMake(x - halfSize, y - halfSize, indicatorSize, indicatorSize);
        touchIndicatorViewList[index-1].layer.cornerRadius = halfSize;

        NSString *coordinateText = [NSString stringWithFormat:@"(%d, %d)", (int)(x*scale), (int)(y*scale)];
        // iAntsControlAbilityLog(@"moveIndicator coordinateText:(%d,%d)", (int)(x*scale), (int)(y*scale));
        UIFont *font = [UIFont fontWithName: @"Trebuchet MS" size: 11.0f];
        CGSize stringSize = [coordinateText sizeWithFont:font]; 
        CGFloat stringWidth = stringSize.width;

        coordinateView[index-1].coordinateLabel.text = coordinateText;

        coordinateView[index-1].coordinateLabel.frame = CGRectMake(0, 0, stringWidth+5, COORDINATE_VIEW_HEIGHT);
        coordinateView[index-1].frame =  CGRectMake(x + halfSize + 5, y, stringWidth+5, COORDINATE_VIEW_HEIGHT);
    });
}

@end
