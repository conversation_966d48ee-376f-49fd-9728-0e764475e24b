//
//  IAntsOCRManager.m
//  touchtest
//
//  Created by mazhixiang on 2024/12/10.
//

#import "IAntsOCRManager.h"
#import <UIKit/UIKit.h>
#import "../Common.h"
#import "IAntsOCRUtils.h"
#import "UIImage+Utils.h"
#import "../handlers/Screen.h"
#import "./luban/UIImage+Luban_iOS_Extension_h.h"

OBJC_EXTERN UIImage *_UICreateScreenUIImage(void);
OBJC_EXTERN void CARenderServerRenderDisplay(kern_return_t a, CFStringRef b, IOSurfaceRef surface, int x, int y);
OBJC_EXTERN CGImageRef UICreateCGImageFromIOSurface(IOSurfaceRef surface);

typedef NSDictionary *(^OCRResultHandler)(id data, NSError **error);

@implementation IAntsOCRManager

// 截图出来是白色的
+ (UIImage *)screenshot
{
    CGSize screenSize = [UIScreen mainScreen].bounds.size;
    float scale = [UIScreen mainScreen].scale;
    
    NSInteger width, height;
    // setup the width and height of the framebuffer for the device
    if ([[UIDevice currentDevice] userInterfaceIdiom] == UIUserInterfaceIdiomPhone) {
        // iPhone frame buffer is Portrait
        width = screenSize.width * scale;
        height = screenSize.height * scale;
    } else {
        // iPad frame buffer is Landscape
        width = screenSize.height * scale;
        height = screenSize.width * scale;
    }
    
    NSInteger bytesPerElement = 4;
    NSInteger bytesPerRow = bytesPerElement * width;
    NSInteger totalBytes = bytesPerRow * height;

    NSDictionary *properties = @{
        (__bridge NSString *)kIOSurfaceIsGlobal:@YES,
        (__bridge NSString *)kIOSurfaceBytesPerElement:@(bytesPerElement),
        (__bridge NSString *)kIOSurfaceBytesPerRow:@(bytesPerRow),
        (__bridge NSString *)kIOSurfaceWidth:@(width),
        (__bridge NSString *)kIOSurfaceHeight:@(height),
        (__bridge NSString *)kIOSurfacePixelFormat:@(0x42475241),//'ARGB'
        (__bridge NSString *)kIOSurfaceAllocSize:@(bytesPerRow * height)
    };
    
    IOSurfaceRef surface = IOSurfaceCreate((__bridge CFDictionaryRef)properties);
    
    IOSurfaceLock(surface, 0, nil);
    CARenderServerRenderDisplay(0, CFSTR("LCD"), surface, 0, 0);
    void * baseAddr = IOSurfaceGetBaseAddress(surface);
    NSData * data = [NSData dataWithBytes:baseAddr length:totalBytes];
    IOSurfaceUnlock(surface, 0, 0);
    
    CGDataProviderRef provider = CGDataProviderCreateWithData(NULL, data.bytes, data.length, NULL);
    CGImageRef imageRef = CGImageCreate(width, height, 8, 8*bytesPerElement, bytesPerRow,
                                        CGColorSpaceCreateDeviceRGB(),
                                        kCGBitmapByteOrder32Host | kCGImageAlphaFirst,
                                        provider, NULL, false, kCGRenderingIntentDefault);
    
    UIImage * image = [UIImage imageWithCGImage:imageRef];

    CGImageRelease(imageRef);
    CFRelease(surface);
    
    return image;
}

+ (UIImage *)screenshotByIOSurface {
    Boolean isiPad8orUp = false;

    CGFloat scale = [UIScreen mainScreen].scale;
    CGSize screenSize = [UIScreen mainScreen].bounds.size;

    int height = (int)(screenSize.height * scale);
    int width = (int)(screenSize.width * scale);

    // check whether it is ipad8 or later
    NSString *searchText = getDeviceName();

    NSRange range = [searchText rangeOfString:@"^iPad[8-9]|iPad[1-9][0-9]+" options:NSRegularExpressionSearch];
    if (range.location != NSNotFound) { // ipad pro (3rd) or later
        isiPad8orUp = true;
    }

    if (isiPad8orUp)
    {
        if (width < height)
        {
            int temp = width;
            width = height;
            height = temp;
        }
    }
    else
    {
        if (width > height)
        {
            int temp = width;
            width = height;
            height = temp;
        }
    }

    int bytesPerElement = 4;
    int bytesPerRow = roundUp(bytesPerElement * width, 32);

    NSNumber *IOSurfaceBytesPerElement = [NSNumber numberWithInteger:bytesPerElement]; 
    NSNumber *IOSurfaceBytesPerRow = [NSNumber numberWithInteger:bytesPerRow]; // don't know why but it should be a multiple of 32
    NSNumber *IOSurfaceAllocSize = [NSNumber numberWithInteger:bytesPerRow * height]; 
    NSNumber *nheight = [NSNumber numberWithInteger:height]; 
    NSNumber *nwidth = [NSNumber numberWithInteger:width]; 
    NSNumber *IOSurfacePixelFormat = [NSNumber numberWithInteger:1111970369]; 
    NSNumber *IOSurfaceIsGlobal = [NSNumber numberWithInteger:1]; 

    NSDictionary *properties = [[NSDictionary alloc] initWithObjectsAndKeys:IOSurfaceAllocSize, @"IOSurfaceAllocSize"
                                , IOSurfaceBytesPerElement, @"IOSurfaceBytesPerElement", IOSurfaceBytesPerRow, @"IOSurfaceBytesPerRow", nheight, @"IOSurfaceHeight", 
                                IOSurfaceIsGlobal, @"IOSurfaceIsGlobal", IOSurfacePixelFormat, @"IOSurfacePixelFormat", nwidth, @"IOSurfaceWidth", nil];    

    IOSurfaceRef screenSurface = IOSurfaceCreate((__bridge CFDictionaryRef)(properties));

    properties = nil;
    
    IOSurfaceLock(screenSurface, 0, NULL);
    CARenderServerRenderDisplay(0, CFSTR("LCD"), screenSurface, 0, 0);
        
    CGImageRef cgImageRef = nil;
    if (screenSurface) {
        cgImageRef = UICreateCGImageFromIOSurface(screenSurface);
        int targetWidth = CGImageGetWidth(cgImageRef);
        int targetHeight = CGImageGetHeight(cgImageRef);

        if (isiPad8orUp) // rotate 90 degrees counterclockwise
        {
            CGColorSpaceRef colorSpaceInfo = CGImageGetColorSpace(cgImageRef);
            CGContextRef bitmap;

            //if (sourceImage.imageOrientation == UIImageOrientationUp || sourceImage.imageOrientation == UIImageOrientationDown) {
                bitmap = CGBitmapContextCreate(NULL, targetHeight, targetWidth, CGImageGetBitsPerComponent(cgImageRef), CGImageGetBytesPerRow(cgImageRef), colorSpaceInfo, kCGImageAlphaPremultipliedFirst);
            //} else {
                //bitmap = CGBitmapContextCreate(NULL, targetHeight, targetWidth, CGImageGetBitsPerComponent(cgImageRef), CGImageGetBytesPerRow(imageRef), colorSpaceInfo, bitmapInfo);

            //}   
            CGFloat degrees = -90.f;
            CGFloat radians = degrees * (M_PI / 180.f);

            CGContextTranslateCTM (bitmap, 0.5*targetHeight, 0.5*targetWidth);
            CGContextRotateCTM (bitmap, radians);
            CGContextTranslateCTM (bitmap, -0.5*targetWidth, -0.5*targetHeight);

            CGContextDrawImage(bitmap, CGRectMake(0, 0, targetWidth, targetHeight), cgImageRef);
            
            CGImageRelease(cgImageRef);
            cgImageRef = nil;
            cgImageRef = CGBitmapContextCreateImage(bitmap);

            CGColorSpaceRelease(colorSpaceInfo);
            colorSpaceInfo = nil;
            CGContextRelease(bitmap);
            bitmap = nil;
        }
    }
    UIImage *image = nil;
    if (cgImageRef) {
       image = [UIImage imageWithCGImage:cgImageRef];
       CGImageRelease(cgImageRef);
       cgImageRef = nil;
       // UIImageWriteToSavedPhotosAlbum(image, nil, nil, nil);
    }
    IOSurfaceUnlock(screenSurface, 0, NULL);
    CFRelease(screenSurface);
    screenSurface = nil;

    return image;
}
+ (NSString *)privateApiScreenshot:(OCRStrategy)strategy content:(NSDictionary *)content error:(NSError **)error success:(bool *)success {
    iAntsControlAbilityLog(@"privateApiScreenshot...");
    // UIImage *image = _UICreateScreenUIImage();
    UIImage *image = [self screenshotByIOSurface];
    if (!image) {
        if (success) {
            *success = false;
        }
        if (error) {
            *error = [NSError errorWithDomain:@"IAntsOCR" code:1000 userInfo:@{NSLocalizedDescriptionKey: @"image is nil."}];
        }
        return nil;
    }
    if (strategy == SAVE) {
        // 不压缩 将 UIImage 转换为 PNG 数据
        // NSData *imageData = UIImagePNGRepresentation(image);
        NSData *lubanImgData = [UIImage lubanCompressImage:image];
        image = nil;
        NSString *ret = [self saveScreenShot:content[@"fileName"] image:lubanImgData error:error success:success];
        lubanImgData = nil;
        return ret;
    } else {
        // 不压缩
        // NSString *base64ImageStr = [image base64String];
        NSData *lubanImgData = [UIImage lubanCompressImage:image];
        NSString *lubanBase64String = [lubanImgData base64EncodedStringWithOptions:NSDataBase64EncodingEndLineWithCarriageReturn];
        image = nil;
        NSString *ret = [self parseImageElements:strategy content:content image:lubanBase64String error:error success:success];
        lubanImgData = nil;
        lubanBase64String = nil;
        return ret;
    }
}

+ (NSString *)saveScreenShot:(NSString *)fileNameSuffix image:(NSData *)imageData error:(NSError **)error success:(bool *)success {
    // 获取当前时间
    NSDate *now = [NSDate date];
    
    // 格式化时间
    NSDateFormatter *dateFormatter = [[NSDateFormatter alloc] init];
    [dateFormatter setDateFormat:@"yyyy-MM-dd_HH-mm-ss"];
    NSString *formattedDate = [dateFormatter stringFromDate:now];
    
    // 构建文件名
    NSString *fileName = [NSString stringWithFormat:@"%@_%@.png", formattedDate, fileNameSuffix];
    
    // 目录
    NSString *exclick = [NSString stringWithFormat:@"%@var/mobile/Library/Caches/com.data.iantscore/exclick", getJbPath()];
    
    // 构建完整文件路径
    NSString *filePath = [exclick stringByAppendingPathComponent:fileName];
    
    // 保存数据到文件
    BOOL suc = [[NSFileManager defaultManager] createFileAtPath:filePath contents:imageData attributes:nil];
    
    if (!suc) {
        iAntsControlAbilityLog(@"Failed to save image to %@", filePath);
        if (success) {
            *success = false;
        }
        if (error) {
            *error = [NSError errorWithDomain:@"IAntsOCR" code:1000 userInfo:@{NSLocalizedDescriptionKey: [NSString stringWithFormat:@"Failed to save image. %@", filePath]}];
        }
        return nil;
    } else {
        iAntsControlAbilityLog(@"Image saved successfully at %@", filePath);
        return filePath;
    }
}

+ (NSString *)parseImageElements:(OCRStrategy)strategy content:(NSDictionary *)content image:(NSString *)base64ImageStr error:(NSError **)error success:(bool *)success {
    if (!base64ImageStr || base64ImageStr.length == 0) {
        iAntsControlAbilityLog(@"%s image is nil...", __FUNCTION__);
        if (success) {
            *success = false;
        }
        if (error) {
            *error = [NSError errorWithDomain:@"IAntsOCR" code:1000 userInfo:@{NSLocalizedDescriptionKey: @"base64ImageStr is nil."}];
        }
        return nil;
    }
    iAntsControlAbilityLog(@"%s parseImageElements...", __FUNCTION__);
    NSDictionary *result = [self parseImageElementsByStrategy:strategy content:content image:base64ImageStr error:error success:success];
    if (success && !(*success)) { // 失败
        return nil;
    }
    if (!result || result.count == 0) {
        iAntsControlAbilityLog(@"OCR 结果为空。");
        if (success) {
            *success = false;
        }
        if (error) {
            *error = [NSError errorWithDomain:@"IAntsOCR" code:1000 userInfo:@{NSLocalizedDescriptionKey: @"OCR result is empty."}];
        }
        return nil;
    }

     // 序列化 NSArray 到 NSData
    NSData *data = [NSJSONSerialization dataWithJSONObject:result options:NSJSONWritingPrettyPrinted error:error];
    
    if (data) {
        NSString *jsonString = [[NSString alloc] initWithData:data encoding:NSUTF8StringEncoding];
        iAntsControlAbilityLog(@"OCR Successfully converted NSArray to NSData. %@", jsonString);
        return jsonString;
    }
    return nil;
    
}


+ (NSDictionary *)parseImageElementsByStrategy:(OCRStrategy)strategy content:(NSDictionary *)content image:(NSString *)base64ImageStr error:(NSError **)error success:(bool *)success {
    NSDictionary *result = nil;
    switch(strategy) {
        case DTOCR:
            {
                NSString *url = @"http://**************:10000/ocr";
                NSDictionary *header = @{
                    @"Authorization": @"796ab64f4ddcb39744f09f053e7fb1f59b826a701875dc228893f480dd09d923",
                    @"Content-Type": @"application/json"
                };
                NSDictionary *body = @{@"data": @[base64ImageStr]};
                NSInteger timeout = 600;
                OCRResultHandler block = ^(id data, NSError **error) {
                    if (data && [data isKindOfClass:[NSArray class]] && [data count] == 1) {
                        CGSize imageSize = [IAntsOCRUtils getImageSize4Base64Str:base64ImageStr];
                        NSDictionary *result = [IAntsOCRUtils transferImageElements:data[0] imageSize:imageSize];
                        return result;
                    }
                    if (error) {
                        *error = [NSError errorWithDomain:@"IAntsOCR" code:1010 userInfo:@{NSLocalizedDescriptionKey: [NSString stringWithFormat:@"OCRResultHandler error [strategy: %ld, data: %@]", strategy, data]}];
                    }
                    return @{};
                };
                result = [self executePostRequest:url header:header body:body timeout:timeout block:block error:error success:success];
                break;
            }
        case SHOWUI:
            {
                NSString *url = @"http://**************:7861/api/coordinate";
                NSDictionary *header = @{
                    @"Content-Type": @"application/json"
                };
                NSDictionary *body = @{@"prompt": content[@"prompt"], @"img_base64": base64ImageStr};
                NSInteger timeout = 600;
                OCRResultHandler block = ^(id data, NSError **error) {
                    if (data && [data isKindOfClass:[NSArray class]] && [data count] == 2) {
                        return @{@"x": data[0], @"y": data[1]};
                    }
                    if (error) {
                        *error = [NSError errorWithDomain:@"IAntsOCR" code:1010 userInfo:@{NSLocalizedDescriptionKey: [NSString stringWithFormat:@"OCRResultHandler error [strategy: %ld, data: %@]", strategy, data]}];
                    }
                    return @{};
                };
                result = [self executePostRequest:url header:header body:body timeout:timeout block:block error:error success:success];
                break;
            }
        case STATE:
            {
                NSString *url = @"http://**************:7861/api/state/predict";
                NSDictionary *header = @{
                    @"Content-Type": @"application/json"
                };
                NSDictionary *body = @{@"brand": content[@"brand"], @"img_base64": base64ImageStr};
                NSInteger timeout = 600;
                OCRResultHandler block = ^(id data, NSError **error) {
                    if (data && [data isKindOfClass:[NSString class]]) {
                        return @{@"state": data};
                    }
                    if (error) {
                        *error = [NSError errorWithDomain:@"IAntsOCR" code:1010 userInfo:@{NSLocalizedDescriptionKey: [NSString stringWithFormat:@"OCRResultHandler error [strategy: %ld, data: %@]", strategy, data]}];
                    }
                    return @{};
                };
                result = [self executePostRequest:url header:header body:body timeout:timeout block:block error:error success:success];
                break;
            }
        case CAPTCHA:
            {
                NSString *url = @"http://**************:7861/api/captcha";
                NSDictionary *header = @{
                    @"Content-Type": @"application/json"
                };
                NSDictionary *body = @{@"brand": content[@"brand"], @"type": content[@"type"], @"img_base64": base64ImageStr};
                NSInteger timeout = 60;
                OCRResultHandler block = ^(id data, NSError **error) {
                    if (data && [data isKindOfClass:[NSNumber class]]) {
                        return @{@"x": data};
                    }
                    if (error) {
                        *error = [NSError errorWithDomain:@"IAntsOCR" code:1010 userInfo:@{NSLocalizedDescriptionKey: [NSString stringWithFormat:@"OCRResultHandler error [strategy: %ld, data: %@]", strategy, data]}];
                    }
                    return @{};
                };
                result = [self executePostRequest:url header:header body:body timeout:timeout block:block error:error success:success];
                break;
            }
        default:
            {
                if (success) {
                    *success = false;
                }
                if (error) {
                    *error = [NSError errorWithDomain:@"IAntsOCR" code:1010 userInfo:@{NSLocalizedDescriptionKey: [NSString stringWithFormat:@"OCR strategy %ld is invalid.", strategy]}];
                }
                break;
            }
    }
    return result;

}

+ (NSDictionary *)executePostRequest:(NSString *)url header:(NSDictionary *)header body:(NSDictionary *)body timeout:(NSInteger)timeout block:(OCRResultHandler)block error:(NSError **)error success:(bool *)success {
    iAntsControlAbilityLog(@"%s", __FUNCTION__);
    iAntsControlAbilityLog(@"url: %@", url);
    iAntsControlAbilityLog(@"header: %@", header);
    iAntsControlAbilityLog(@"body: %@", body);
    iAntsControlAbilityLog(@"timeout: %ld", timeout);

    if (!url || url.length == 0) {
        if (success) {
            *success = false;
        }
        if (error) {
            *error = [NSError errorWithDomain:@"IAntsOCR" code:1010 userInfo:@{NSLocalizedDescriptionKey: @"url is invalid."}];
        }
        return nil;
    }
    __block NSDictionary *result = nil;
    __block NSError *bError = nil;
    NSURLSession *session = [NSURLSession sharedSession];
    NSMutableURLRequest *request = [[NSMutableURLRequest alloc] initWithURL:[NSURL URLWithString:url]];
    request.HTTPMethod = @"POST";
    NSInteger to = timeout > 0 ? timeout : 20;
    request.timeoutInterval = to;
    if (body) {
        NSData *jsonData = [NSJSONSerialization dataWithJSONObject:body options:0 error:&bError];
        if (!jsonData) {
            if (error) {
                *error = bError;
            }
            return result;
        }
        request.HTTPBody = jsonData;
    }
    
    if (header) {
        request.allHTTPHeaderFields = header;
    }
    
    dispatch_semaphore_t semaphore = dispatch_semaphore_create(0);

    NSURLSessionDataTask *task = [session dataTaskWithRequest:request completionHandler:^(NSData * _Nullable data, NSURLResponse * _Nullable response, NSError * _Nullable reqError) {
        if (reqError) {
            iAntsControlAbilityLog(@"%@ Error: %@", request.URL, reqError);
            bError = reqError;
            dispatch_semaphore_signal(semaphore);
            return;
        }
        
        NSError *parseError;
        NSDictionary *responseObject = [NSJSONSerialization JSONObjectWithData:data options:0 error:&parseError];
        if (parseError) {
            iAntsControlAbilityLog(@"%@ Parse Error: %@", request.URL, parseError);
            bError = parseError;
            dispatch_semaphore_signal(semaphore);
            return;
        }
        iAntsControlAbilityLog(@"responseObject: %@", responseObject);
        NSNumber *code = responseObject[@"code"];
        if ([code integerValue] != 200) {
            bError = [NSError errorWithDomain:@"IAntsOCR" code:1020 userInfo:@{NSLocalizedDescriptionKey: [NSString stringWithFormat:@"request [%@] error: %@", request.URL, responseObject]}];
        } else {
            id data = responseObject[@"data"];
            result = block(data, &bError);
        }
        dispatch_semaphore_signal(semaphore);
    }];
    [task resume];
    // 等待信号量，即等待任务完成
    dispatch_time_t time = dispatch_time(DISPATCH_TIME_NOW, (int64_t)(to * NSEC_PER_SEC));
    int wait = dispatch_semaphore_wait(semaphore, time);
    if (bError) {
        if (success){
            *success = false;
        }
        if (error) {
            *error = bError;
        }
        
        return nil;
    }
    if (wait == 0) {
        if (!result || result.count == 0) {
            if (success){
                *success = false;
            }
            if (error) {
                *error = [NSError errorWithDomain:@"IAntsOCR" code:1020 userInfo:@{NSLocalizedDescriptionKey: [NSString stringWithFormat:@"%@ request result is empty.", url]}];
            }
            result = nil;
        }
    } else {
        if (success){
            *success = false;
        }
        if (error) {
            *error = [NSError errorWithDomain:@"IAntsOCR" code:1020 userInfo:@{NSLocalizedDescriptionKey: [NSString stringWithFormat:@"%@ request timeout.", url]}];
        }
        iAntsControlAbilityLog(@"%@ request 执行超时。", url);
    }
    return result;
}
@end
