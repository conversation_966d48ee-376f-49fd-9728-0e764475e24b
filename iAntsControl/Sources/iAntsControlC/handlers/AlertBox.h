#ifndef ALERT_BOX_H
#define ALERT_BOX_H
#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>
#include <stdint.h>
#import <CoreFoundation/CoreFoundation.h>

void showAlertBox(NSString* title, NSString* content, int dismissTime);
void showAlertBoxAsync(NSString* title, NSString* content, int dismissTime);
void showAlertBoxFromRawData(UInt8 *eventData, NSError **error);

void showAdvancedAlertBox(
    NSString *title,                      // 可选：弹窗标题
    NSString *content,                    // 可选：弹窗内容
    NSString *defaultButtonTitle,         // 可选：主按钮标题
    NSString *alternateButtonTitle,       // 可选：备用按钮标题
    NSString *otherButtonTitle,           // 可选：第三按钮标题
    NSArray<NSString *> *checkBoxTitles,  // 可选：复选框标题数组
    NSArray<NSString *> *textFieldTitles, // 可选：输入框标题数组
    NSArray<NSString *> *textFieldValues, // 可选：输入框初始值数组
    NSArray<NSString *> *popUpTitles,     // 可选：下拉菜单选项数组
    NSNumber *popUpSelection,             // 可选：下拉菜单默认选中索引
    NSNumber *progressValue,              // 可选：进度条数值（0.0~1.0）
    NSURL *iconURL,                       // 可选：自定义图标URL
    int dismissTime                       // 可选：超时时间（秒）
);

#endif