#ifndef TASK_H
#define TASK_H
#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>
#include <stdint.h>
#import <CoreFoundation/CoreFoundation.h>

#define TASK_PERFORM_TOUCH 10
#define TASK_PROCESS_BRING_FOREGROUND 11
#define TASK_SHOW_ALERT_BOX 12
#define TASK_RUN_SHELL 13
#define TASK_TOUCH_RECORDING_START 14
#define TASK_TOUCH_RECORDING_STOP 15
#define TASK_CRAZY_TAP 16
#define TASK_RAPID_FIRE_TAP 17
#define TASK_USLEEP 18
#define TASK_TEMPLATE_MATCH 21
#define TASK_SHOW_TOAST 22
#define TASK_COLOR_PICKER 23
#define TASK_TEXT_INPUT 24
#define TASK_GET_DEVICE_INFO 25
#define TASK_TOUCH_INDICATOR 26
#define TASK_TEXT_RECOGNIZER 27
#define TAS<PERSON>_COLOR_SEARCHER 28
#define TASK_SCREEN_SHOT 29
#define TASK_KILL_APP 30
#define TASK_SCREEN_SHOT_SHOWUI 31
#define TASK_PERFORM_TOUCH_PROPORTION 32
#define TASK_SCREEN_SHOT_SAVE 33
#define TASK_SCREEN_SHOT_STATE 34
#define TASK_CAPTCHA_HANDLE 35
#define TASK_TEXT_INPUT_BY_PASTE 36

#define TASK_TEST 99

void processTask(UInt8 *buff, CFWriteStreamRef writeStreamRef = NULL);
static int getTaskType(UInt8* dataArray);

#endif