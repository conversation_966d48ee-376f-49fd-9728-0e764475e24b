#include "BKUserEventTimer.h"
#import <QuartzCore/QuartzCore.h>

#import <UIKit/UIKit.h>
#import <Foundation/Foundation.h>
#import <SpringBoard/SpringBoard.h>
#import <objc/runtime.h>

#include <sys/sysctl.h>
#include <sys/xattr.h>
#include <substrate.h>
#include <math.h>
#include <dlfcn.h>

#include <mach/mach.h>
#import <CoreGraphics/CoreGraphics.h>
#import <IOKit/hid/IOHIDService.h>

#include<mach-o/dyld.h>

#include <stdlib.h>
#include "server/SocketConfig.h"
#include "server/SocketServer.h"
// iAntsSocketHandler.h import 已迁移至 UI.xm
#include <stdio.h>
#include <unistd.h>
#include <signal.h>

#include <notify.h>
#include "CFUserNotification.h"
#import <os/lock.h>

#include "handlers/Touch.h"
#include "Common.h"
#include "handlers/Screen.h"
#include "handlers/AlertBox.h"
#include "handlers/Popup.h"
#include "handlers/Record.h"
#include "handlers/Toast.h"
#include "TouchIndicator/TouchIndicatorWindow.h"
#include "hook/AutoUnlock.h"
#import "LogC.h"


#define DEBUG_MODE

#ifdef DEBUG_MODE
#define CHECKER true
#else
#define CHECKER !isExpired
#endif

#ifndef YES
#define YES ((BOOL)1)
#endif

#ifndef NO
#define NO ((BOOL)0)
#endif

int daemonSock = -1;

typedef struct eventInfo_s* eventInfo;
//typedef struct Node* llNodePtr;
typedef struct eventData_s* eventDataPtr;


const int TOUCH_EVENT_ARR_LEN = 20;

Boolean isCrazyTapping = false;
Boolean isRecording = false;

eventInfo touchEventArr[TOUCH_EVENT_ARR_LEN] = {0};

// 记录 iAntsCore 连续异常的次数
int iAntsCoreErrorCount = 0;
int iAntsCoreMaxErrorCount = 3;

//llNodePtr eventLinkedListHead = NULL;


Boolean isInitializedSuccess = true;

int getDaemonSocket();
void *(*IOHIDEventAppendEventOld)(IOHIDEventRef parent, IOHIDEventRef child);


float getRandomNumberFloat(float min, float max);

int getTaskType(UInt8* dataArray);

void handle_event (void* target, void* refcon, IOHIDServiceRef service, IOHIDEventRef event);

void setSenderIdCallback(void* target, void* refcon, IOHIDServiceRef service, IOHIDEventRef event);

static void stopCrazyTapCallback();
void crazyTapTimeUpCallback();
void stopCrazyTap();
void processTask(UInt8 *buff);

BOOL openPopUpByDoubleVolumnDown = true;

// -------------
IOHIDEventSystemClientRef ioHIDEventSystemForPopupDectect = NULL;
PopupWindow *popupWindow;

void stopCrazyTap()
{
    isCrazyTapping = false;
}

/*
A callback to stop crazy tap.

Note: using a callback to stop crazy tap is because the socket server may not respond while crazy tapping
*/
static void stopCrazyTapCallback()
{
    stopCrazyTap();
}


void crazyTapTimeUpCallback(int sig)
{
    LOGI(@"crazy tap stop.");
    stopCrazyTap();
}

void dontPutThisFileIntoIda()
{
    return;
}

void becauseTheSourceCodeWillBeReleasedAtGithub()
{
    return;
}

void repoNameIsIOS13SimulateTouch()
{
    return;
}

/*
Get the sender id and unregister itself.
*/
static CFTimeInterval startTime = 0;
// perform some action
static void popupWindowCallBack(void* target, void* refcon, IOHIDServiceRef service, IOHIDEventRef event)
{
    if (!openPopUpByDoubleVolumnDown)
        return;
    if (IOHIDEventGetType(event) == kIOHIDEventTypeKeyboard)
    {
        if (IOHIDEventGetIntegerValue(event, kIOHIDEventFieldKeyboardUsage) == 234 && IOHIDEventGetIntegerValue(event, kIOHIDEventFieldKeyboardDown) == 0)
        {
            CFTimeInterval currentTime = CACurrentMediaTime();
            if (currentTime - startTime > 0.4)
            {
                startTime = CACurrentMediaTime();
                return;
            }

            if (isRecordingStart())
            {
                stopRecording();
                showAlertBox(@"Recording stopped", [NSString stringWithFormat:@"Your touch record has been saved. Please open zxtouch app to see your script list. This record script is located at %@recording", getScriptsFolder()], 999);
                [popupWindow show];
                return;
            }
            if (![popupWindow isShown])
            {
                [popupWindow show];
            }
            else
            {
                [popupWindow hide];
            }
        }
    }
}

/**
Start the callback for setting sender id
*/
void startPopupListeningCallBack()
{
    LOGI(@"startPopupListeningCallBack is called!");
    ioHIDEventSystemForPopupDectect = IOHIDEventSystemClientCreate(kCFAllocatorDefault);

    IOHIDEventSystemClientScheduleWithRunLoop(ioHIDEventSystemForPopupDectect, CFRunLoopGetCurrent(), kCFRunLoopDefaultMode);
    IOHIDEventSystemClientRegisterEventCallback(ioHIDEventSystemForPopupDectect, (IOHIDEventSystemClientEventCallback)popupWindowCallBack, NULL, NULL);
}

// Boolean initActivatorInstance()
// {
//     dlopen("/usr/lib/libactivator.dylib", RTLD_LAZY);
//     Class la = objc_getClass("LAActivator");
//     if (la) { //libactivator is installed
//         activatorInstance = [[ActivatorListener alloc] init];
        
//         LAActivator* activator = [la sharedInstance];
//         if (activator.isRunningInsideSpringBoard)
//         {
//             //[activator unregisterListenerWithName:@"com.data.iantscore.plugin.touchability"];
//             [activator registerListener:activatorInstance 
//                                             forName:@"com.data.iantscore.plugin.touchability"];
//         }

//     }


//     return true;
// }

Boolean initConfig()
{
    // read config file
    // check whether config file exist
    NSString *configFilePath = getCommonConfigFilePath();

    if (![[NSFileManager defaultManager] fileExistsAtPath:configFilePath]) // if missing, then use the default value
    {
        //showAlertBox(@"Error", configFilePath, 999);
        LOGI(@"unable to get config file. File not found. Using default value. Path: %@", configFilePath);
        return true;
    }
    // read indicator color from the config file
    NSDictionary *config = [[NSDictionary alloc] initWithContentsOfFile:configFilePath];
    if ([config[@"touch_indicator"][@"show"] boolValue])
    {
        NSError *err = nil;
        startTouchIndicator(&err);
        if (err)
        {
            showAlertBox(@"Error", [NSString stringWithFormat:@"Cannot start touch indicator, error info: %@", err], 999);
        }
    }

    if (config[@"double_click_volume_show_popup"])
    {
        LOGI(@"show popup %d", [config[@"double_click_volume_show_popup"] boolValue]);
        openPopUpByDoubleVolumnDown = [config[@"double_click_volume_show_popup"] boolValue];
    }

    return true;
}

Boolean init()
{
    initConfig();

    return true;
}


UIViewController *topMostController()
{
    UIWindow *mainWindow = [[UIApplication sharedApplication] keyWindow];
    UIViewController *topController = mainWindow.rootViewController;

    while (topController.presentedViewController)
    {
        topController = topController.presentedViewController;
    }
    return topController;
}

// iAntsCore 健康检查回调函数
void checkIAntsCoreHealthCallback(NSTimer *timer)
{
    dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
        LOGI(@"[iAntsTouchAbility] 检查iAntsCore服务状态...");
        
        // 创建HTTP请求检查服务状态
        NSURL *url = [NSURL URLWithString:@"http://127.0.0.1:6888"];
        NSMutableURLRequest *request = [NSMutableURLRequest requestWithURL:url];
        request.HTTPMethod = @"GET";
        request.timeoutInterval = 10.0; // 10秒超时
        
        // 使用同步请求（因为在后台线程中）
        NSError *error = nil;
        NSHTTPURLResponse *response = nil;
        NSData *data = [NSURLConnection sendSynchronousRequest:request 
                                             returningResponse:&response 
                                                         error:&error];
        
        
        if (error) {
            LOGE(@"[iAntsTouchAbility] iAntsCore健康检查失败: %@", error.localizedDescription);
            iAntsCoreErrorCount++;
        } else if (response.statusCode == 200 && data) {
            NSString *responseString = [[NSString alloc] initWithData:data encoding:NSUTF8StringEncoding];
            if ([responseString containsString:@"OK"]) {
                iAntsCoreErrorCount = 0;
                LOGI(@"[iAntsTouchAbility] iAntsCore服务运行正常");
            } else {
                LOGW(@"[iAntsTouchAbility] iAntsCore服务响应异常: %@", responseString);
                iAntsCoreErrorCount++;
            }
        } else {
            LOGW(@"[iAntsTouchAbility] iAntsCore服务响应状态码异常: %ld", (long)response.statusCode);
            iAntsCoreErrorCount++;
        }
        
        // 如果服务不健康，使用 system2 重启iAntsCore
        if (iAntsCoreErrorCount > iAntsCoreMaxErrorCount) {
            LOGW(@"[iAntsTouchAbility] ⚠️ iAntsCore服务异常，尝试重启...");
            
            // 使用 system2 执行系统命令重启iAntsCore
            int killResult = system2("killall iAntsCore", NULL, NULL);
            LOGI(@"[iAntsTouchAbility] 执行killall命令，返回值: %d", killResult);
        }
    });
}


%ctor{
    NSLog(@"[iAntsTouchAbility] SpringBoard hook has been loaded!");
    LOGI(@"[iAntsTouchAbility] SpringBoard hook has been loaded!");
}

%hook SpringBoard

- (void)applicationDidFinishLaunching:(id)arg1
{
    LOGI(@"applicationDidFinishLaunching");

    dispatch_sync(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
        LOGI(@"Inside dispatch_async - Start");
        CGFloat screen_scale = [[UIScreen mainScreen] scale];
        LOGI(@"screen_scale: %f", screen_scale);


        CGFloat width = [UIScreen mainScreen].bounds.size.width * screen_scale;
        CGFloat height = [UIScreen mainScreen].bounds.size.height * screen_scale;
        LOGI(@"Calculated screen width: %f, height: %f", width, height);

        [Screen setScreenSize:(width<height?width:height) height:(width>height?width:height)];   
        LOGI(@"Finished setting screen size");
 
        //CFNotificationCenterAddObserver(CFNotificationCenterGetDarwinNotifyCenter(), NULL, (CFNotificationCallback)stopCrazyTapCallback, CFSTR("com.zjx.crazytap.stop"), NULL, CFNotificationSuspensionBehaviorDeliverImmediately);
        popupWindow = [[PopupWindow alloc] init];

        initSenderId();
        LOGI(@"Finished initSenderId");

        startPopupListeningCallBack();
        LOGI(@"Finished startPopupListeningCallBack");

        // init touch screensize. Temporarily put this line here. Will be removed.
        initTouchGetScreenSize();
        LOGI(@"Finished initTouchGetScreenSize");

        // init other things
        if (!init())
        {
            LOGE(@"init Error!");
            return;
        }
        
        // 永不锁屏默认开启，如果UserDefaults中没有设置，就默认为开启
        BOOL preventAutoLockExists = [[NSUserDefaults standardUserDefaults] objectForKey:@"iants_prevent_autolock"] != nil;
        if (!preventAutoLockExists) {
            LOGI(@"永不锁屏设置不存在，默认设置为开启");
            [[NSUserDefaults standardUserDefaults] setBool:YES forKey:@"iants_prevent_autolock"];
            [[NSUserDefaults standardUserDefaults] synchronize];
            setPreventAutoLock(YES);
        } else {
            // 读取已保存的设置
            BOOL preventAutoLock = [[NSUserDefaults standardUserDefaults] boolForKey:@"iants_prevent_autolock"];
            LOGI(@"永不锁屏设置: %@", preventAutoLock ? @"开启" : @"关闭");
            setPreventAutoLock(preventAutoLock);
        }
    });

    %orig;
    
    //启动老的socket服务器
    dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
        LOGI(@"启动socket服务器");
        socketServer();
    });

    // 使用定时器监控 iAntsCore 是否卡死（每5分钟检查一次）
    LOGI(@"[iAntsTouchAbility] 启动iAntsCore健康检查定时器，每5分钟检查一次");
    [NSTimer scheduledTimerWithTimeInterval:300.0 // 5分钟 = 300秒
                                    repeats:YES
                                      block:^(NSTimer * _Nonnull timer) {
                                          checkIAntsCoreHealthCallback(timer);
                                      }];
}

%end
