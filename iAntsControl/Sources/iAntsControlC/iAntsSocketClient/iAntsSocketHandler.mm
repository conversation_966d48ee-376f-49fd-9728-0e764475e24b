//
//  iAntsSocketHandler.mm
//  iAntsTouchAbility
//
//  Created by iAnts on 2024/06/06.
//

#import "iAntsSocketHandler.h"
#import "../Common.h"
#import "../handlers/Touch.h"
#import "../handlers/Screen.h"
#import "../handlers/AlertBox.h"
#import "../handlers/Toast.h"
#import "../handlers/Record.h"
#import "../handlers/ScreenShot.h"
#import "../hook/AutoUnlock.h"
#import "LogC.h"

// MARK: - 触摸功能实现

// 简单的单点触摸函数实现
void performSimpleTouch(float x, float y, int touchType) {
    // 创建一个简单的触摸事件数据包
    UInt8 touchData[14]; // 1个计数 + 13个数据
    touchData[0] = '1'; // 触摸点数量为1
    
    // 构造触摸数据：type(1) + index(2) + x(5) + y(5)
    touchData[1] = '0' + touchType; // 触摸类型
    touchData[2] = '0'; // 手指索引高位
    touchData[3] = '0'; // 手指索引低位
    
    // X坐标 (5位数，包含小数点)
    int xInt = (int)(x * 10);
    for (int i = 4; i <= 8; i++) {
        touchData[i] = '0' + (xInt / (int)pow(10, 8-i)) % 10;
    }
    
    // Y坐标 (5位数，包含小数点)
    int yInt = (int)(y * 10);
    for (int i = 9; i <= 13; i++) {
        touchData[i] = '0' + (yInt / (int)pow(10, 13-i)) % 10;
    }
    
    // 调用现有的触摸处理函数
    performTouchFromRawData(touchData);
}

// MARK: - iAntsSocketHandler私有接口扩展

@interface iAntsSocketHandler ()

// 私有辅助方法声明
- (void)performTouchAtX:(int32_t)x y:(int32_t)y touchType:(int)touchType;

@end

// MARK: - iAntsSocketHandler实现

@implementation iAntsSocketHandler {
    IAntsSocketClient *_socketClient;
}

+ (instancetype)shared {
    static iAntsSocketHandler *sharedInstance = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        sharedInstance = [[self alloc] init];
    });
    return sharedInstance;
}

- (instancetype)init {
    if (self = [super init]) {
        // 获取WebSocket客户端单例
        _socketClient = [IAntsSocketClient shared];
        _socketClient.delegate = self;
        
        // 注册消息处理器
        [self registerMessageHandlers];
        
        LOGI(@"[iAntsSocketHandler] 初始化完成，已设置为WebSocket客户端的委托");
    }
    return self;
}

#pragma mark - Public Methods

// 移除initializeAndConnect方法 - 强制要求调用者提供完整配置参数

- (BOOL)initializeAndConnectWithConfig:(IAntsSocketConfig *)config {
    // 检查是否已经连接
    if ([self isConnected]) {
        LOGI(@"[iAntsSocketHandler] WebSocket已经连接，跳过重复初始化");
        return YES;
    }
    
    LOGI(@"[iAntsSocketHandler] 使用配置初始化WebSocket客户端");
    LOGI(@"[iAntsSocketHandler] 服务器: %@", config.serverURL);
    LOGI(@"[iAntsSocketHandler] 最大重连次数: %ld", (long)config.maxReconnectAttempts);
    LOGI(@"[iAntsSocketHandler] 自动重连: %@", config.autoReconnectEnabled ? @"启用" : @"禁用");
    LOGI(@"[iAntsSocketHandler] 心跳机制: %@", config.heartbeatEnabled ? @"启用" : @"禁用");
    LOGI(@"[iAntsSocketHandler] 心跳间隔: %.1f秒", config.heartbeatInterval);
    
    // 使用WebSocket客户端进行初始化和连接
    BOOL success = [_socketClient initializeAndConnectWithConfig:config];
    if (success) {
        LOGI(@"[iAntsSocketHandler] WebSocket客户端初始化成功");
        LOGI(@"[iAntsTouchAbility] iAnts WebSocket客户端启动成功");
    } else {
        LOGE(@"[iAntsSocketHandler] WebSocket客户端初始化失败");
        LOGE(@"[iAntsTouchAbility] iAnts WebSocket客户端启动失败");
    }
    
    return success;
}

- (void)disconnect {
    [_socketClient disconnect];
    LOGI(@"[iAntsSocketHandler] WebSocket客户端已断开连接");
}

- (BOOL)isConnected {
    return _socketClient.connected;
}

- (IAntsSocketClient *)socketClient {
    return _socketClient;
}

#pragma mark - Private Methods

- (void)registerMessageHandlers {
    // 注册各种消息类型的处理器
    __weak iAntsSocketHandler *weakSelf = self;
    
    // 心跳消息处理器
    [_socketClient registerHandlerForMessageType:IAntsMessageTypeHeartbeat handler:^(IAntsMessageType messageType, NSDictionary *messageContent) {
        __strong iAntsSocketHandler *strongSelf = weakSelf;
        LOGI(@"[iAntsSocketHandler] 心跳消息处理器被调用，类型: %ld", (long)messageType);
        [strongSelf handleHeartbeatMessage:messageContent];
    }];
    LOGI(@"[iAntsSocketHandler] ✅ 已注册心跳消息处理器，类型: %d", (int)IAntsMessageTypeHeartbeat);
    
    // 弹窗消息处理器
    [_socketClient registerHandlerForMessageType:IAntsMessageTypeSBShowAlert handler:^(IAntsMessageType messageType, NSDictionary *messageContent) {
        __strong iAntsSocketHandler *strongSelf = weakSelf;
        [strongSelf handleAlertMessage:messageContent];
    }];
    
    // Toast消息处理器
    [_socketClient registerHandlerForMessageType:IAntsMessageTypeSBShowToast handler:^(IAntsMessageType messageType, NSDictionary *messageContent) {
        __strong iAntsSocketHandler *strongSelf = weakSelf;
        [strongSelf handleToastMessage:messageContent];
    }];
    
    // 触摸事件处理器
    [_socketClient registerHandlerForMessageType:IAntsMessageTypeSBTouchEvent handler:^(IAntsMessageType messageType, NSDictionary *messageContent) {
        __strong iAntsSocketHandler *strongSelf = weakSelf;
        [strongSelf handleTouchEventMessage:messageContent];
    }];
    
    // 截屏请求处理器
    [_socketClient registerHandlerForMessageType:IAntsMessageTypeSBScreenshot handler:^(IAntsMessageType messageType, NSDictionary *messageContent) {
        __strong iAntsSocketHandler *strongSelf = weakSelf;
        [strongSelf handleScreenshotRequest:messageContent];
    }];
    
    // 锁屏请求处理器
    [_socketClient registerHandlerForMessageType:IAntsMessageTypeSBLockScreen handler:^(IAntsMessageType messageType, NSDictionary *messageContent) {
        __strong iAntsSocketHandler *strongSelf = weakSelf;
        [strongSelf handleLockScreenRequest:messageContent];
    }];
    
    // 解锁请求处理器
    [_socketClient registerHandlerForMessageType:IAntsMessageTypeSBUnlockScreen handler:^(IAntsMessageType messageType, NSDictionary *messageContent) {
        __strong iAntsSocketHandler *strongSelf = weakSelf;
        [strongSelf handleUnlockScreenRequest:messageContent];
    }];
    
    // 前台应用请求处理器
    [_socketClient registerHandlerForMessageType:IAntsMessageTypeSBForegroundApp handler:^(IAntsMessageType messageType, NSDictionary *messageContent) {
        __strong iAntsSocketHandler *strongSelf = weakSelf;
        [strongSelf handleForegroundAppRequest:messageContent];
    }];
    
    // 通知请求处理器
    [_socketClient registerHandlerForMessageType:IAntsMessageTypeSBNotification handler:^(IAntsMessageType messageType, NSDictionary *messageContent) {
        __strong iAntsSocketHandler *strongSelf = weakSelf;
        [strongSelf handleNotificationMessage:messageContent];
    }];
    
    // 高级弹窗处理器
    [_socketClient registerHandlerForMessageType:IAntsMessageTypeSBAdvancedAlertBox handler:^(IAntsMessageType messageType, NSDictionary *messageContent) {
        __strong iAntsSocketHandler *strongSelf = weakSelf;
        [strongSelf handleAdvancedAlertMessage:messageContent];
    }];
    
    // 错误消息处理器
    [_socketClient registerHandlerForMessageType:IAntsMessageTypeError handler:^(IAntsMessageType messageType, NSDictionary *messageContent) {
        __strong iAntsSocketHandler *strongSelf = weakSelf;
        [strongSelf handleErrorMessage:messageContent];
    }];

    // 粘贴框消息处理器
    [_socketClient registerHandlerForMessageType:IAntsMessageTypeSBShowPaste handler:^(IAntsMessageType messageType, NSDictionary *messageContent) {
        __strong iAntsSocketHandler *strongSelf = weakSelf;
        [strongSelf handlePasteMessage:messageContent];
    }];
    
    LOGI(@"[iAntsSocketHandler] 已注册所有消息处理器");
}

#pragma mark - Message Handlers

// 处理心跳消息（在后台线程中执行）
- (void)handleHeartbeatMessage:(NSDictionary *)content {
    NSString *version = content[@"version"] ?: @"未知版本";
    LOGI(@"[iAntsSocketHandler] 收到服务端心跳消息，版本: %@", version);
    LOGI(@"[iAntsSocketHandler] 心跳消息内容: %@", content);
    
    // 在主线程发送UI通知（因为UI.xm中监听此通知进行UI更新）
    dispatch_async(dispatch_get_main_queue(), ^{
        NSDictionary *userInfo = @{@"version": version};
        [[NSNotificationCenter defaultCenter] postNotificationName:@"iAntsWebSocketDidReceiveHeartbeat" 
                                                            object:nil 
                                                          userInfo:userInfo];
    });
    
    LOGI(@"[iAntsSocketHandler] 已发送心跳通知: iAntsWebSocketDidReceiveHeartbeat");
}

// 处理弹窗消息
- (void)handleAlertMessage:(NSDictionary *)content {
    NSString *title = content[@"title"] ?: @"";
    NSString *alertContent = content[@"content"] ?: @"";
    int32_t dismissTime = [content[@"dismissTime"] intValue];
    
    LOGI(@"[iAntsSocketHandler] 收到弹窗请求 - 标题: %@, 内容: %@, 自动关闭时间: %d秒", 
         title, alertContent, dismissTime);
    
    // 直接调用 AlertBox.xm 中的实现
    showAlertBoxAsync(title, alertContent, dismissTime > 0 ? dismissTime : 999);
}

// 处理Toast消息
- (void)handleToastMessage:(NSDictionary *)content {
    NSString *toastContent = content[@"content"] ?: @"";
    int32_t type = [content[@"type"] intValue];
    float duration = [content[@"duration"] floatValue];
    int32_t position = [content[@"position"] intValue];
    int32_t fontSize = [content[@"fontSize"] intValue];
    
    LOGI(@"[iAntsSocketHandler] 收到Toast请求: %@, 类型: %d, 持续时间: %.1f, 位置: %d, 字体大小: %d", 
         toastContent, type, duration, position, fontSize);
    
    // 直接调用 Toast.xm 中的实现
    [Toast showToastWithContent:toastContent 
                           type:type > 0 ? type : 1
                       duration:duration > 0 ? duration : 3.0
                       position:position 
                       fontSize:fontSize > 0 ? fontSize : 16];
}

// 处理触摸事件消息
- (void)handleTouchEventMessage:(NSDictionary *)content {
    int32_t x = [content[@"x"] intValue];
    int32_t y = [content[@"y"] intValue];
    NSString *action = content[@"action"] ?: @"tap";
    
    LOGI(@"[iAntsSocketHandler] 收到触摸事件 - 坐标: (%d, %d), 动作: %@", x, y, action);
    
    // 构造触摸数据包，格式：count + type + index + x + y
    UInt8 touchData[14]; // 1个计数 + 13个数据
    touchData[0] = '1'; // 触摸点数量为1
    
    // 根据动作字符串确定触摸类型
    int touchType = TOUCH_DOWN; // 默认为按下
    if ([action isEqualToString:@"down"] || [action isEqualToString:@"press"]) {
        touchType = TOUCH_DOWN;
    } else if ([action isEqualToString:@"up"] || [action isEqualToString:@"release"]) {
        touchType = TOUCH_UP;
    } else if ([action isEqualToString:@"move"] || [action isEqualToString:@"drag"]) {
        touchType = TOUCH_MOVE;
    } else if ([action isEqualToString:@"tap"] || [action isEqualToString:@"click"]) {
        // 点击事件：先按下再抬起
        [self performTouchWithData:touchData x:x y:y touchType:TOUCH_DOWN];
        
        // 延时50ms后抬起，在block中重新创建触摸数据
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.05 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            // 在block中重新创建触摸数据，避免引用外部数组
            [self performTouchAtX:x y:y touchType:TOUCH_UP];
        });
        return;
    }
    
    // 执行单个触摸动作
    [self performTouchWithData:touchData x:x y:y touchType:touchType];
}

// 处理屏幕信息请求
- (void)handleScreenInfoRequest:(NSDictionary *)content {
    LOGI(@"[iAntsSocketHandler] 收到屏幕信息请求");
    
    // 直接调用 Screen.xm 中的实现获取屏幕信息
    CGFloat screenWidth = [Screen getScreenWidth];
    CGFloat screenHeight = [Screen getScreenHeight];
    CGFloat scale = [Screen getScale];
    
    LOGI(@"[iAntsSocketHandler] 屏幕信息 - 尺寸: %.0fx%.0f, 缩放比例: %.1f", 
         screenWidth, screenHeight, scale);
    
    // TODO: 发送屏幕信息响应
    // 需要定义响应消息类型和格式
    NSDictionary *responseContent = @{
        @"width": @(screenWidth),
        @"height": @(screenHeight),
        @"scale": @(scale)
    };
    
    LOGI(@"[iAntsSocketHandler] 屏幕信息已获取: %@", responseContent);
    // 这里可以通过WebSocket发送响应，但需要先定义响应消息类型
}

// 处理截屏请求
- (void)handleScreenshotRequest:(NSDictionary *)content {
    LOGI(@"[iAntsSocketHandler] 收到截屏请求");
    
    // 在后台线程执行截屏操作
    dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
        // 直接调用 ScreenShot.xm 中的实现
        NSError *error = nil;
        bool success = false;
        NSString *screenshotBase64 = getScreenShotFromRawData(NULL, &error, &success);
        
        dispatch_async(dispatch_get_main_queue(), ^{
            if (success && screenshotBase64 && screenshotBase64.length > 0) {
                LOGI(@"[iAntsSocketHandler] 截屏成功，长度: %lu", (unsigned long)screenshotBase64.length);
                
                // TODO: 发送截屏数据响应
                LOGI(@"[iAntsSocketHandler] 截屏数据已获取，需要定义响应消息类型发送给客户端");
            } else {
                LOGE(@"[iAntsSocketHandler] 截屏失败: %@", error ? error.localizedDescription : @"未知错误");
                
                // 发送错误消息
                [self sendErrorMessage:[NSString stringWithFormat:@"截屏失败: %@", 
                                       error ? error.localizedDescription : @"未知错误"]];
            }
        });
    });
}

// 处理锁屏请求
- (void)handleLockScreenRequest:(NSDictionary *)content {
    LOGI(@"[iAntsSocketHandler] 收到锁屏请求");
    
    // 使用现有的锁屏功能
    // TODO: 实现锁屏功能
    // 可能需要使用私有API
    
    LOGI(@"[iAntsSocketHandler] 锁屏功能待实现");
    [self sendErrorMessage:@"锁屏功能尚未实现"];
}

// 处理解锁请求
- (void)handleUnlockScreenRequest:(NSDictionary *)content {
    LOGI(@"[iAntsSocketHandler] 收到解锁请求");
    
    // 使用现有的解锁功能
    // TODO: 调用解锁相关的函数
    // 可能需要使用私有API或现有的AutoUnlock功能
    
    LOGI(@"[iAntsSocketHandler] 解锁功能待实现");
    [self sendErrorMessage:@"解锁功能尚未实现"];
}

// 处理前台应用请求
- (void)handleForegroundAppRequest:(NSDictionary *)content {
    LOGI(@"[iAntsSocketHandler] 收到前台应用请求");
    
    // 获取前台应用信息
    // TODO: 实现获取前台应用的逻辑
    // 可能需要使用SpringBoard的相关API
    
    LOGI(@"[iAntsSocketHandler] 前台应用获取功能待实现");
    [self sendErrorMessage:@"前台应用获取功能尚未实现"];
}

// 处理通知消息
- (void)handleNotificationMessage:(NSDictionary *)content {
    NSString *title = content[@"title"] ?: @"";
    NSString *body = content[@"body"] ?: @"";
    
    LOGI(@"[iAntsSocketHandler] 收到通知请求 - 标题: %@, 内容: %@", title, body);
    
    // 显示系统通知或使用弹窗作为替代方案
    // 这里使用弹窗作为通知的替代实现
    showAlertBoxAsync(title, body, 999);
    
    LOGI(@"[iAntsSocketHandler] 已显示通知内容");
}

// 处理高级弹窗消息
- (void)handleAdvancedAlertMessage:(NSDictionary *)content {
    NSString *title = content[@"title"] ?: @"";
    NSString *alertContent = content[@"content"] ?: @"";
    NSString *defaultButtonTitle = content[@"defaultButtonTitle"] ?: @"";
    NSString *alternateButtonTitle = content[@"alternateButtonTitle"] ?: @"";
    NSString *otherButtonTitle = content[@"otherButtonTitle"] ?: @"";
    int32_t dismissTime = [content[@"dismissTime"] intValue];
    
    LOGI(@"[iAntsSocketHandler] 收到高级弹窗请求 - 标题: %@, 内容: %@", title, alertContent);
    LOGI(@"[iAntsSocketHandler] 按钮标题 - 默认: %@, 备选: %@, 其他: %@", 
         defaultButtonTitle, alternateButtonTitle, otherButtonTitle);
    LOGI(@"[iAntsSocketHandler] 自动关闭时间: %d秒", dismissTime);
    
    // 直接调用 AlertBox.xm 中的高级弹窗实现
    showAdvancedAlertBox(
        title,
        alertContent,
        defaultButtonTitle,
        alternateButtonTitle,
        otherButtonTitle,
        content[@"checkBoxTitles"], // NSArray
        content[@"textFieldTitles"], // NSArray
        content[@"textFieldValues"], // NSArray
        content[@"popUpTitles"], // NSArray
        content[@"popUpSelection"], // NSNumber
        content[@"progressValue"], // NSNumber
        content[@"iconURL"], // NSString
        dismissTime > 0 ? dismissTime : 999
    );
}

// 处理错误消息
- (void)handleErrorMessage:(NSDictionary *)content {
    NSString *errorText = content[@"text"] ?: @"未知错误";
    
    LOGE(@"[iAntsSocketHandler] 收到错误消息: %@", errorText);
    LOGE(@"[iAntsTouchAbility] Socket错误: %@", errorText);
    
    // 显示错误信息
    showAlertBoxAsync(@"Socket错误", errorText, 999);
}

#pragma mark - Helper Methods

// 执行触摸操作的辅助方法（使用传入的数组）
- (void)performTouchWithData:(UInt8 *)touchData x:(int32_t)x y:(int32_t)y touchType:(int)touchType {
    // 构造触摸数据包
    touchData[1] = '0' + touchType; // 触摸类型
    touchData[2] = '0'; // 手指索引高位
    touchData[3] = '0'; // 手指索引低位
    
    // X坐标 (5位数，包含小数点)
    int xInt = (int)(x * 10);
    for (int i = 4; i <= 8; i++) {
        touchData[i] = '0' + (xInt / (int)pow(10, 8-i)) % 10;
    }
    
    // Y坐标 (5位数，包含小数点)
    int yInt = (int)(y * 10);
    for (int i = 9; i <= 13; i++) {
        touchData[i] = '0' + (yInt / (int)pow(10, 13-i)) % 10;
    }
    
    // 直接调用 Touch.mm 中的实现
    performTouchFromRawData(touchData);
}

// 执行触摸操作的辅助方法（内部创建数组，适用于block中调用）
- (void)performTouchAtX:(int32_t)x y:(int32_t)y touchType:(int)touchType {
    // 在方法内部创建触摸数据包
    UInt8 touchData[14]; // 1个计数 + 13个数据
    touchData[0] = '1'; // 触摸点数量为1
    touchData[1] = '0' + touchType; // 触摸类型
    touchData[2] = '0'; // 手指索引高位
    touchData[3] = '0'; // 手指索引低位
    
    // X坐标 (5位数，包含小数点)
    int xInt = (int)(x * 10);
    for (int i = 4; i <= 8; i++) {
        touchData[i] = '0' + (xInt / (int)pow(10, 8-i)) % 10;
    }
    
    // Y坐标 (5位数，包含小数点)
    int yInt = (int)(y * 10);
    for (int i = 9; i <= 13; i++) {
        touchData[i] = '0' + (yInt / (int)pow(10, 13-i)) % 10;
    }
    
    // 直接调用 Touch.mm 中的实现
    performTouchFromRawData(touchData);
}

// 发送错误消息的辅助方法
- (void)sendErrorMessage:(NSString *)errorText {
    NSDictionary *content = @{@"text": errorText ?: @"未知错误"};
    BOOL success = [_socketClient sendMessageType:IAntsMessageTypeError content:content];
    if (!success) {
        LOGE(@"[iAntsSocketHandler] 发送错误消息失败: %@", errorText);
    }
}

#pragma mark - IAntsSocketClientDelegate Implementation

// WebSocket连接成功回调（在后台线程中执行）
- (void)webSocketDidConnect {
    LOGI(@"[iAntsSocketHandler] WebSocket连接成功");
    LOGI(@"[iAntsTouchAbility] Socket连接成功");
    
    // 在主线程发送UI通知（因为UI.xm中监听此通知进行UI更新）
    dispatch_async(dispatch_get_main_queue(), ^{
        [[NSNotificationCenter defaultCenter] postNotificationName:@"iAntsWebSocketDidConnect" object:nil];
    });
}

// WebSocket连接断开回调（在后台线程中执行）
- (void)webSocketDidDisconnect:(NSError *)error {
    LOGI(@"[iAntsSocketHandler] WebSocket连接断开: %@", error ? error.localizedDescription : @"正常断开");
    LOGI(@"[iAntsTouchAbility] Socket连接断开: %@", error ? error.localizedDescription : @"正常断开");
    
    // 在主线程发送UI通知（因为UI.xm中监听此通知进行UI更新）
    dispatch_async(dispatch_get_main_queue(), ^{
        NSDictionary *userInfo = nil;
        if (error) {
            userInfo = @{@"error": error.localizedDescription};
        }
        [[NSNotificationCenter defaultCenter] postNotificationName:@"iAntsWebSocketDidDisconnect" 
                                                            object:nil 
                                                          userInfo:userInfo];
    });
}

// 收到WebSocket消息回调（通过消息处理器已经处理，这里主要用于日志记录）
- (void)webSocketDidReceiveMessage:(IAntsMessageType)messageType content:(NSDictionary *)messageContent {
    // 消息已经通过注册的处理器处理，这里只做日志记录
    LOGI(@"[iAntsSocketHandler] 委托收到消息: 类型=%ld, 内容键数量=%lu", 
         (long)messageType, (unsigned long)messageContent.count);
}

// 发送消息前回调（可选）
- (void)webSocketWillSendMessage:(IAntsMessageType)messageType content:(NSDictionary *)messageContent {
    LOGI(@"[iAntsSocketHandler] 即将发送消息: 类型=%ld", (long)messageType);
}

// 发送消息结果回调（可选）
- (void)webSocketDidSendMessage:(IAntsMessageType)messageType success:(BOOL)success error:(NSError *)error {
    if (success) {
        LOGI(@"[iAntsSocketHandler] 消息发送成功: 类型=%ld", (long)messageType);
    } else {
        LOGE(@"[iAntsSocketHandler] 消息发送失败: 类型=%ld, 错误=%@", 
             (long)messageType, error ? error.localizedDescription : @"未知错误");
    }
}


- (void)handlePasteMessage:(NSDictionary *)content {
    NSString *pasteContent = content[@"content"] ?: @"";
    
    LOGI(@"[iAntsSocketHandler] 收到粘贴框请求 - 内容: %@", pasteContent);
    
    UIPasteboard *pasteboard = [UIPasteboard generalPasteboard];
    pasteboard.string = pasteContent;
}

@end 