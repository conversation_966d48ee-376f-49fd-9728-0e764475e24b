// See http://iphonedevwiki.net/index.php/Logos

#import <CoreFoundation/CoreFoundation.h>
#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>
#import <Foundation/NSUserDefaults+Private.h>
#import <dlfcn.h>
#import <substrate.h>
#import "../Common.h"

UIViewController *autoUnlockTopMostController() {
    UIWindow *mainWindow = [[UIApplication sharedApplication] keyWindow];
    UIViewController *topController = mainWindow.rootViewController;
    while (topController.presentedViewController) {
        topController = topController.presentedViewController;
    }
    return topController;
}

@interface MCProfileConnection : NSObject
+ (instancetype)sharedConnection;
- (NSDictionary *)effectiveParametersForValueSetting:(NSString *)arg1;
- (void)setValue:(id)arg1 forSetting:(NSString *)arg2;
@end

@interface SBUIController : NSObject
+ (id)sharedInstanceIfExists;
+ (id)sharedInstance;
- (void)ACPowerChanged; // 交流电源(插入USB)状态改变通知
- (BOOL)isOnAC; // 是否已连接电源(插入USB)，状态改变时候可使用此方法获取当前状态
-(int)batteryCapacityAsPercentage;
@end

static NSString * nsDomainString = @"com.data.iantscore.plugin.AutoUnlock";
static NSString * nsNotificationString = @"com.data.iantscore.plugin.AutoUnlock/preferences.changed";
static BOOL enabled;
static BOOL preventAutoLock = NO; // 添加永不锁屏开关变量
static int origMaxInactivity = 30; ///< 默认的锁屏时间
static bool isLocked();

// 应用锁屏时间设置的内部函数
static void applyAutoLockSetting(BOOL neverLock) {
    MCProfileConnection *profileConn = [objc_getClass("MCProfileConnection") sharedConnection];
    
    if (neverLock) {
        // 先保存当前的系统设置
        NSDictionary *params = [profileConn effectiveParametersForValueSetting:@"maxInactivity"];
        int currentMaxInactivity = [params[@"value"] intValue];
        if (currentMaxInactivity != INT32_MAX) {
            origMaxInactivity = currentMaxInactivity;
            iAntsControlAbilityLog(@"[AutoUnlock] 保存原有锁屏时间: %d秒", origMaxInactivity);
        }
        
        // 设置为永不锁屏
        [profileConn setValue:[NSNumber numberWithInt:INT32_MAX] forSetting:@"maxInactivity"];
        iAntsControlAbilityLog(@"[AutoUnlock] 已设置永不锁屏");
    } else {
        // 恢复为系统默认锁屏时间
        [profileConn setValue:[NSNumber numberWithInt:origMaxInactivity] forSetting:@"maxInactivity"];
        iAntsControlAbilityLog(@"[AutoUnlock] 已恢复系统默认锁屏时间: %d秒", origMaxInactivity);
    }
}

// 对外公开的设置永不锁屏状态的函数
void setPreventAutoLock(BOOL prevent) {
    // 如果状态没有变化，则不需要重新应用设置
    if (preventAutoLock == prevent) {
        iAntsControlAbilityLog(@"[AutoUnlock] 永不锁屏状态没有变化，仍为: %@", prevent ? @"开启" : @"关闭");
        return;
    }
    
    preventAutoLock = prevent;
    iAntsControlAbilityLog(@"[AutoUnlock] 永不锁屏状态设置为: %@", prevent ? @"开启" : @"关闭");
    
    // 立即应用新设置
    applyAutoLockSetting(preventAutoLock);
    
    // 如果关闭了永不锁屏，且启用了自动解锁功能，则根据充电状态重新设置
    if (!preventAutoLock && enabled) {
        SBUIController *controller = [objc_getClass("SBUIController") sharedInstance];
        // 手动触发充电状态变化的处理，会根据当前充电状态重新设置锁屏时间
        [controller ACPowerChanged];
    }
}

// 获取当前永不锁屏状态
BOOL getPreventAutoLock() {
    return preventAutoLock;
}

static void notificationCallback(CFNotificationCenterRef center, void *observer, CFStringRef name, const void *object, CFDictionaryRef userInfo) {
    NSNumber * enabledValue = (NSNumber *)[[NSUserDefaults standardUserDefaults] objectForKey:@"enabled" inDomain:nsDomainString];
    enabled = (enabledValue)? [enabledValue boolValue] : YES;
}


%hook SBUIController
- (void)ACPowerChanged {//充电状态
    %orig;
    
    // 如果永不锁屏开关打开，则优先使用永不锁屏设置，不受充电状态影响
    if (preventAutoLock) {
        applyAutoLockSetting(YES); // 确保始终保持永不锁屏状态
        iAntsControlAbilityLog(@"[AutoUnlock] ACPowerChanged - 保持永不锁屏状态");
        return;
    }
    
    if(enabled){
        MCProfileConnection *profileConn = [objc_getClass("MCProfileConnection") sharedConnection];
        int maxInactivityValue = INT32_MAX;
        if([self isOnAC]){
            NSDictionary *params = [profileConn effectiveParametersForValueSetting:@"maxInactivity"];
            int maxInactivity = [params[@"value"] intValue];
            if(maxInactivity != INT32_MAX){
                origMaxInactivity = maxInactivity;
            }
            maxInactivityValue = INT32_MAX;
            iAntsControlAbilityLog(@"[AutoUnlock] 充电状态 - 设置永不锁屏");
        }else{
            maxInactivityValue = origMaxInactivity;
            iAntsControlAbilityLog(@"[AutoUnlock] 未充电状态 - 恢复原始锁屏时间: %d秒", origMaxInactivity);
        }

        if(maxInactivityValue > 0){
            [profileConn setValue:[NSNumber numberWithInt:maxInactivityValue] forSetting:@"maxInactivity"];
        }
    }
}
%end



%ctor {
    iAntsControlAbilityLog(@"[plugin] AutoUnlock SpringBoard hook has been loaded!");
    
    // 读取持久化的永不锁屏设置
    preventAutoLock = [[NSUserDefaults standardUserDefaults] boolForKey:@"iants_prevent_autolock"];
    if (preventAutoLock) {
        iAntsControlAbilityLog(@"[AutoUnlock] 从UserDefaults恢复永不锁屏设置: 开启");
        // 将在初始化完成后应用设置
    }
    
    dispatch_async(dispatch_get_main_queue(), ^{
        UIViewController *rootViewController = autoUnlockTopMostController();
        if (rootViewController) {
            // UIAlertController *alert = [UIAlertController alertControllerWithTitle:@"Launching" message:@"Plugin: Auto Unlock" preferredStyle:UIAlertControllerStyleAlert];
            //[rootViewController presentViewController:alert animated:YES completion:nil];
            
            // 在2秒后自动关闭弹窗
            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(2 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                //[alert dismissViewControllerAnimated:YES completion:nil];
            });
        } else {
            iAntsControlAbilityLog(@"[plugin] AutoUnlock Unable to find rootViewController.");
        }
    });

    // Set variables on start up
    notificationCallback(NULL, NULL, NULL, NULL, NULL);

    // Register for 'PostNotification' notifications
    CFNotificationCenterAddObserver(CFNotificationCenterGetDarwinNotifyCenter(), NULL, notificationCallback, (CFStringRef)nsNotificationString, NULL, CFNotificationSuspensionBehaviorCoalesce);

    // 在初始化完成后，如果永不锁屏设置为开启，应用设置
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        if (preventAutoLock) {
            applyAutoLockSetting(YES);
        }
    });

    // Add any personal initializations
    %init();
}
