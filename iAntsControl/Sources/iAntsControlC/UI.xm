#import <QuartzCore/QuartzCore.h>
#import <UIKit/UIKit.h>
#import <Foundation/Foundation.h>
#import <SpringBoard/SpringBoard.h>
#import <Foundation/NSDistributedNotificationCenter.h>
#include "Common.h"
#include "hook/AutoUnlock.h"
#include "iAntsSocketClient/iAntsSocketHandler.h"
#import "LogC.h"

// 浮窗和面板的 tag
static const NSInteger kFloatingBtnTag = 9999;
static const NSInteger kPanelTag       = 9998;
static const NSInteger kBackdropTag    = 10000;

// HTTP API 基本地址
static NSString * const kHTTPBaseURL = @"http://127.0.0.1:6888";

// NSUserDefaults 中保存序列号的键名
static NSString * const kSerialNumberKey = @"iants_device_serial_number";
// 测试序列号值，用于判断是否需要重新获取
static NSString * const kTestSerialValue = @"未知";

// 全局状态缓存
static BOOL gIAntsIsOnline = NO;
static NSString *gIAntsVersion = @"未知版本";
static NSString *gIAntsSerialNumber = @"未知";
static NSString *gTaskStatus = @"无任务";

// WebSocket连接状态
static BOOL gWebSocketConnected = NO;

// 通用HTTP请求方法
static NSDictionary* performHTTPRequest(NSString *path, NSString *method, NSDictionary *body) {
    NSURL *url = [NSURL URLWithString:[kHTTPBaseURL stringByAppendingString:path]];
    NSMutableURLRequest *request = [NSMutableURLRequest requestWithURL:url];
    request.HTTPMethod = method;
    
    if (body) {
        NSError *error = nil;
        NSData *jsonData = [NSJSONSerialization dataWithJSONObject:body options:0 error:&error];
        if (!error) {
            request.HTTPBody = jsonData;
            [request setValue:@"application/json" forHTTPHeaderField:@"Content-Type"];
        }
    }
    
    __block NSDictionary *result = nil;
    __block BOOL finished = NO;
    
    NSURLSession *session = [NSURLSession sharedSession];
    [[session dataTaskWithRequest:request completionHandler:^(NSData *data, NSURLResponse *response, NSError *error) {
        if (!error && data) {
            NSHTTPURLResponse *httpResponse = (NSHTTPURLResponse *)response;
            if (httpResponse.statusCode == 200) {
                NSError *jsonError = nil;
                id jsonObject = [NSJSONSerialization JSONObjectWithData:data options:0 error:&jsonError];
                if (!jsonError && [jsonObject isKindOfClass:[NSDictionary class]]) {
                    result = jsonObject;
                }
            }
        }
        finished = YES;
    }] resume];
    
    // 简单的同步等待结果 (仅用于非UI线程调用)
    NSDate *timeoutDate = [NSDate dateWithTimeIntervalSinceNow:5.0]; // 5秒超时
    while (!finished && [[NSDate date] compare:timeoutDate] == NSOrderedAscending) {
        [[NSRunLoop currentRunLoop] runMode:NSDefaultRunLoopMode beforeDate:[NSDate dateWithTimeIntervalSinceNow:0.1]];
    }
    
    return result;
}

// HTTP降级：获取版本和在线状态的函数
static void checkIAntsStatusHTTP(BOOL *isOnlinePtr, NSString * __strong *versionPtr) {
    LOGI(@"[HTTP降级] 使用HTTP检查状态");
    NSDictionary *response = performHTTPRequest(@"/version", @"GET", nil);
    BOOL online = (response && [[response objectForKey:@"code"] intValue] == 200);
    NSString *ver = @"未知版本";
    
    if (online) {
        ver = [response objectForKey:@"data"] ?: @"未知版本";
    }
    
    if (isOnlinePtr) *isOnlinePtr = online;
    if (versionPtr) *versionPtr = ver;
}

// HTTP降级：通知服务端WebSocket异常
static void notifyWebSocketError() {
    dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
        NSDictionary *response = performHTTPRequest(@"/websocket/error", @"POST", @{
            @"error": @"WebSocket连接失败",
            @"timestamp": @([[NSDate date] timeIntervalSince1970])
        });
        if (response && [[response objectForKey:@"code"] intValue] == 200) {
            LOGI(@"[HTTP降级] WebSocket异常通知已发送");
        } else {
            LOGW(@"[HTTP降级] WebSocket异常通知失败");
        }
    });
}

// 获取设备序列号
static NSString* getDeviceSerialNumber() {
    NSDictionary *response = performHTTPRequest(@"/device/serialNumber", @"GET", nil);
    if (response && [[response objectForKey:@"code"] intValue] == 200) {
        NSString *serialNumber = [response objectForKey:@"data"];
        if (serialNumber && ![serialNumber isEqualToString:@"未知"]) {
            // 缓存有效的序列号
            [[NSUserDefaults standardUserDefaults] setObject:serialNumber forKey:kSerialNumberKey];
            [[NSUserDefaults standardUserDefaults] synchronize];
            return serialNumber;
        }
        return kTestSerialValue;
    }
    return kTestSerialValue;
}

// 从缓存中获取设备序列号
static NSString* getCachedSerialNumber() {
    NSString *cachedSerial = [[NSUserDefaults standardUserDefaults] objectForKey:kSerialNumberKey];
    return cachedSerial ?: kTestSerialValue;
}

// 发送重启iAntsCore请求
static void sendRestartAntsRequest() {
    NSDictionary *response = performHTTPRequest(@"/config/restart", @"POST", @{});
    if (response && [[response objectForKey:@"code"] intValue] == 200) {
        LOGI(@"重启iAnts请求已发送，服务将在3秒后重启");
    } else {
        LOGE(@"重启iAnts请求失败");
    }
}

// 发送更新iAntsCore请求
static void sendUpdateAntsRequest() {
    NSDictionary *response = performHTTPRequest(@"/config/update", @"POST", @{});
    if (response && [[response objectForKey:@"code"] intValue] == 200) {
        LOGI(@"更新iAnts请求已发送");
    } else {
        LOGE(@"更新iAnts请求失败");
    }
}

static void sendRebootUserSpaceRequest() {
    NSDictionary *response = performHTTPRequest(@"/tools/userspace", @"POST", @{});
    if (response && [[response objectForKey:@"code"] intValue] == 200) {
        LOGI(@"重启用户空间请求已发送");
    } else {
        LOGE(@"重启用户空间请求失败，尝试直接执行 launchctl reboot userspace");
        
        // HTTP请求失败时的降级方案：直接调用execShellCommand执行launchctl命令
        @try {
            system2("launchctl reboot userspace", NULL, NULL);
            LOGI(@"launchctl reboot userspace 执行成功");
        } @catch (NSException *exception) {
            LOGE(@"launchctl reboot userspace 执行失败: %@", exception.reason);
        }
    }
}

static void sendUploadSmsRequest(NSString *from, NSString *to, NSString *content) {
    NSDictionary *response = performHTTPRequest(@"/upload/sms", @"POST", @{
        @"from": from,
        @"to": to,
        @"content": content
    });
    if (response && [[response objectForKey:@"code"] intValue] == 200) {
        LOGI(@"上传短信请求已发送");
    } else {
        LOGE(@"上传短信请求失败");
    }
}

// 发送上报
static void sendUploadSerialNumberRequest(NSString *serialNumber) {
    NSDictionary *response = performHTTPRequest(@"/upload/robot_text", @"POST", @{
        @"source": @"UPLOAD",
        @"text": serialNumber
    });
    if (response && [[response objectForKey:@"code"] intValue] == 200) {
        LOGI(@"上报序列号请求已发送成功: %@", serialNumber);
    } else {
        LOGE(@"上报序列号请求失败");
    }
}

// 获取任务状态
static void getTaskStatus(NSString * __strong *taskStatusPtr) {
    NSDictionary *response = performHTTPRequest(@"/task/status", @"GET", nil);
    NSString *status = @"无任务";
    
    if (response && [[response objectForKey:@"code"] intValue] == 200) {
        NSDictionary *data = [response objectForKey:@"data"];
        if (data) {
            NSString *taskName = [data objectForKey:@"name"] ?: @"无任务";
            NSString *taskStatus = [data objectForKey:@"state"] ?: @"无任务";
            status = [NSString stringWithFormat:@"%@:%@", taskName, taskStatus];
        }
    } else if (response && [[response objectForKey:@"code"] intValue] == 404) {
        status = @"无任务";
    }
    
    if (taskStatusPtr) *taskStatusPtr = status;
    gTaskStatus = status;
}

// MARK: - WebSocket相关方法

@interface SpringBoard (iAntsFloating)
- (void)setupFloatingButton;
- (void)initializeWebSocketConnection;
- (void)attemptWebSocketReconnect;
- (void)updateStatusAndUI; // 统一方法用于获取状态和更新UI
- (void)updateAllUIWithStatus; // 仅更新UI，不重新获取状态
- (void)handleDrag:(UIPanGestureRecognizer*)gesture;
- (void)togglePanel;
- (void)switchChanged:(UISwitch*)sw;
- (void)restartIAntsCore;
- (void)updateIAntsCore;
- (void)restartUserSpace;
- (void)startSerialNumberFetchTask;
- (void)tryFetchSerialNumber;
- (void)reportSerialNumber;
- (UIViewController *)topMostController;
- (void)handleFloatingButtonLongPress:(UILongPressGestureRecognizer *)gesture;
- (void)dismissActionMenu;
- (void)startTask;
- (void)stopTask;
- (void)closeTask;
- (void)startRemote;
@end

%ctor{
    // 注册分布式通知接收者，监听 com.iants.sms.updated
    [[NSDistributedNotificationCenter defaultCenter]
        addObserverForName:@"com.iants.sms.updated"
                    object:nil
                     queue:[NSOperationQueue mainQueue]
                usingBlock:^(NSNotification *notification) {
        NSDictionary *smsData = notification.userInfo;
        NSString *from    = smsData[@"from"];
        NSString *to      = smsData[@"to"];
        NSString *content = smsData[@"content"];
        LOGI(@"【短信接收】📥 from: %@, to: %@, content: %@", from, to, content);
        // 调用 iAntsCore 的 API 接口
        sendUploadSmsRequest(from, to, content);
    }];
    
    // 注册WebSocket连接状态通知
    [[NSNotificationCenter defaultCenter]
        addObserverForName:@"iAntsWebSocketDidConnect"
                    object:nil
                     queue:[NSOperationQueue mainQueue]
                usingBlock:^(NSNotification *notification) {
        LOGI(@"[WebSocket通知] 连接成功");
        gWebSocketConnected = YES;
        
        // 连接成功时：设置UI为在线，版本为"获取中"
        gIAntsIsOnline = YES;
        gIAntsVersion = @"获取中";
        
        // 更新UI
        SpringBoard *springBoard = (SpringBoard *)[UIApplication sharedApplication];
        if (springBoard && [springBoard respondsToSelector:@selector(updateAllUIWithStatus)]) {
            [springBoard updateAllUIWithStatus];
        }
    }];
    
    [[NSNotificationCenter defaultCenter]
        addObserverForName:@"iAntsWebSocketDidDisconnect"
                    object:nil
                     queue:[NSOperationQueue mainQueue]
                usingBlock:^(NSNotification *notification) {
        NSString *errorMessage = notification.userInfo[@"error"];
        LOGI(@"[WebSocket通知] 连接断开: %@", errorMessage ?: @"正常断开");
        gWebSocketConnected = NO;
        
        // 断开连接时：设置UI为离线，版本为空
        gIAntsIsOnline = NO;
        gIAntsVersion = @"";
        
        // 更新UI（不再手动处理重连，交给iAntsSocketClient内部处理）
        SpringBoard *springBoard = (SpringBoard *)[UIApplication sharedApplication];
        if (springBoard && [springBoard respondsToSelector:@selector(updateAllUIWithStatus)]) {
            [springBoard updateAllUIWithStatus];
        }
    }];
    
    [[NSNotificationCenter defaultCenter]
        addObserverForName:@"iAntsWebSocketDidReceiveHeartbeat"
                    object:nil
                     queue:[NSOperationQueue mainQueue]
                usingBlock:^(NSNotification *notification) {
        NSString *version = notification.userInfo[@"version"];
        LOGI(@"[WebSocket通知] 收到心跳，版本: %@", version);
        
        BOOL oldOnlineStatus = gIAntsIsOnline;
        
        // 只有收到心跳才将状态改为真正在线并设置版本号
        gIAntsIsOnline = YES;
        gIAntsVersion = version ?: @"未知版本";
        
        // 如果状态有变化，记录日志并更新UI
        if (!oldOnlineStatus) {
            LOGI(@"[状态变化] iAnts 通过WebSocket心跳确认上线");
        }
        
        SpringBoard *springBoard = (SpringBoard *)[UIApplication sharedApplication];
        if (springBoard && [springBoard respondsToSelector:@selector(updateAllUIWithStatus)]) {
            [springBoard updateAllUIWithStatus];
        }
    }];
}

%hook SpringBoard

- (void)applicationDidFinishLaunching:(id)arg1
{
    %orig;
    
    LOGI(@"[SpringBoard] applicationDidFinishLaunching 开始执行");
    
    // 悬浮球面板
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.0 * NSEC_PER_SEC)),
                   dispatch_get_main_queue(), ^{
        LOGI(@"[SpringBoard] 1秒延迟后开始初始化悬浮球");
        
        // 设置悬浮球和面板
        [self setupFloatingButton];
        
        // 初始化WebSocket连接
        [self initializeWebSocketConnection];
        
        // 定期检查iAnts状态 - 仅在WebSocket未连接时使用HTTP降级，每30秒检查一次
        [NSTimer scheduledTimerWithTimeInterval:30.0
                                         target:self
                                       selector:@selector(updateStatusAndUI)
                                       userInfo:nil
                                        repeats:YES];
        
        LOGI(@"[SpringBoard] 状态检查定时器已启动");
        
        // 立即检查一次
        [self updateStatusAndUI];
        
        // 启动序列号获取任务
        [self startSerialNumberFetchTask];
        
        LOGI(@"[SpringBoard] 悬浮球初始化流程完成");
    });
}

// 添加序列号获取任务方法
%new
- (void)startSerialNumberFetchTask {
    // 检查是否已有缓存的序列号
    NSString *cachedSerial = getCachedSerialNumber();
    
    // 如果已有有效序列号，不需要再获取
    if (cachedSerial && ![cachedSerial isEqualToString:kTestSerialValue]) {
        LOGD(@"已有缓存序列号: %@", cachedSerial);
        return;
    }
    
    // 第一次尝试获取序列号
    [self tryFetchSerialNumber];
}

%new
- (void)tryFetchSerialNumber {
    dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
        NSString *serialNumber = getDeviceSerialNumber();
        
        // 如果获取成功，更新缓存和UI
        if (serialNumber && ![serialNumber isEqualToString:kTestSerialValue]) {
            // 更新缓存
            gIAntsSerialNumber = serialNumber;
            
            // 更新UI
            dispatch_async(dispatch_get_main_queue(), ^{
                [self updateAllUIWithStatus];
                
                // 通知其他组件
                [[NSNotificationCenter defaultCenter] 
                 postNotificationName:@"iAntsSerialUpdated" 
                 object:nil 
                 userInfo:@{@"serialNumber": serialNumber}];
            });
            
            LOGI(@"序列号获取成功: %@", serialNumber);
        } else {
            // 获取失败，10秒后重试
            LOGI(@"序列号获取失败，10秒后重试");
            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(10.0 * NSEC_PER_SEC)),
                         dispatch_get_main_queue(), ^{
                [self tryFetchSerialNumber];
            });
        }
    });
}

// MARK: - WebSocket相关方法实现

%new 
- (void)initializeWebSocketConnection {
    // 检查是否已经连接，避免重复初始化
    if ([[iAntsSocketHandler shared] isConnected]) {
        LOGI(@"[WebSocket] WebSocket已经连接，跳过重复初始化");
        gWebSocketConnected = YES;
        return;
    }
    
    LOGI(@"[WebSocket] 启动iAnts WebSocket客户端");
    
    // 使用自定义配置启动WebSocket客户端
    // 架构说明：iAntsSocketHandler -> iAntsSocketClient -> iAntsSocketPB
    IAntsSocketConfig *config = [[IAntsSocketConfig alloc] 
                                initWithServerURL:@"ws://127.0.0.1:6888/sbpipe"
                                maxReconnectAttempts:10
                                autoReconnectEnabled:YES
                                heartbeatEnabled:YES
                                heartbeatInterval:60.0]; // 60秒心跳间隔，确保连接稳定
    
    // 尝试连接
    BOOL success = [[iAntsSocketHandler shared] initializeAndConnectWithConfig:config];
    if (success) {
        LOGI(@"[WebSocket] ✅ iAnts WebSocket客户端启动成功");
        LOGI(@"[WebSocket] ✅ 新架构：OC WebSocket + Protobuf序列化 + 消息处理器");
        LOGI(@"[WebSocket] ✅ 服务器地址：%@", config.serverURL);
        LOGI(@"[WebSocket] ✅ 自动重连：%@，心跳间隔：%.0f秒", 
             config.autoReconnectEnabled ? @"启用" : @"禁用", 
             config.heartbeatInterval);
        gWebSocketConnected = YES;
    } else {
        LOGE(@"[WebSocket] ❌ iAnts WebSocket客户端启动失败");
        LOGE(@"[WebSocket] ❌ 请检查iAntsCore服务是否正常运行");
        LOGE(@"[WebSocket] ❌ 请检查服务器地址：%@", config.serverURL);
        LOGE(@"[WebSocket] ❌ iAntsSocketClient将自动处理重连");
    }
}





// 更新统一获取状态的方法 - 现在优先使用WebSocket，HTTP作为降级
%new
- (void)updateStatusAndUI {
    // 如果WebSocket已连接，则不需要HTTP检查，状态通过心跳实时更新
    if (gWebSocketConnected) {
        LOGD(@"[状态检查] WebSocket已连接，跳过HTTP检查");
        
        // 仅更新序列号和任务状态
        dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
            gIAntsSerialNumber = getCachedSerialNumber();
            
            // 回到主线程更新UI
            dispatch_async(dispatch_get_main_queue(), ^{
                [self updateAllUIWithStatus];
            });
        });
        return;
    }
    
    // WebSocket未连接时，使用HTTP降级检查
    LOGI(@"[状态检查] WebSocket未连接，使用HTTP降级检查");
    dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
        // 更新全局状态缓存 - 一次调用获取所有状态
        BOOL oldOnlineStatus = gIAntsIsOnline;
        
        // 使用本地变量作为中介
        BOOL isOnline = NO;
        NSString *version = nil;
        
        // 一次性获取在线状态和版本
        checkIAntsStatusHTTP(&isOnline, &version);
        
        // 然后更新全局变量
        gIAntsIsOnline = isOnline;
        gIAntsVersion = version;
        gIAntsSerialNumber = getCachedSerialNumber();
        
        // 如果状态有变化，记录日志
        if (oldOnlineStatus != gIAntsIsOnline) {
            LOGI(@"[状态变化] iAnts %@", gIAntsIsOnline ? @"上线" : @"离线");
        }
        
        // 如果HTTP检查发现服务在线，但WebSocket未连接，尝试重连WebSocket
        if (gIAntsIsOnline && !gWebSocketConnected) {
            LOGI(@"[WebSocket] HTTP检查发现服务在线，尝试重连WebSocket");
            dispatch_async(dispatch_get_main_queue(), ^{
                [self initializeWebSocketConnection];
            });
        }
        
        // 回到主线程更新UI
        dispatch_async(dispatch_get_main_queue(), ^{
            [self updateAllUIWithStatus];
        });
    });
}

// 仅更新UI，不重新获取状态
%new
- (void)updateAllUIWithStatus {
    LOGI(@"[UI更新] updateAllUIWithStatus 开始执行");
    
    // 查找悬浮球
    UIWindow *w = UIApplication.sharedApplication.keyWindow;
    if (!w) {
        NSArray *windows = UIApplication.sharedApplication.windows;
        if (windows.count > 0) w = windows[0];
    }
    
    UIView *containerView = [w viewWithTag:kFloatingBtnTag];
    
    if (containerView) {
        // 更新状态标签
        UIVisualEffectView *bubble = (id)[containerView.subviews objectAtIndex:0];
        if (bubble) {
            UILabel *label = (id)[bubble.contentView viewWithTag:100];
            if (label) {
                if (gIAntsIsOnline) {
                    label.text = @"ON";
                    label.textColor = UIColor.systemGreenColor;
                } else {
                    label.text = @"OFF";
                    label.textColor = UIColor.systemRedColor;
                }
            }
        }
        
        // 更新序列号标签
        UILabel *serialLabel = (id)[containerView viewWithTag:101];
        if (serialLabel) {
            serialLabel.text = gIAntsSerialNumber;
        }
    }
    
    // 更新面板（如果正在显示）
    UIView *panel = [w viewWithTag:kPanelTag];
    if (panel) {
        UIView *contentView = [panel.subviews firstObject];
        if (contentView) {
            UILabel *statusLabel = [contentView viewWithTag:102];
            UILabel *versionLabel = [contentView viewWithTag:103];
            UILabel *serialLabel = [contentView viewWithTag:104];
            UILabel *taskStatusValueLabel = [contentView viewWithTag:106];
            
            if (statusLabel) {
                statusLabel.text = [NSString stringWithFormat:@"iAntsCore: %@", gIAntsIsOnline?@"🟢":@"🔴"];
            }
            
            if (versionLabel) {
                versionLabel.text = gIAntsIsOnline? gIAntsVersion : @"offline";
            }
            
            if (serialLabel) {
                serialLabel.text = gIAntsSerialNumber;
            }
            
            if (taskStatusValueLabel) {
                taskStatusValueLabel.text = gTaskStatus;
            }
        }
    }
    
    LOGI(@"[UI更新] updateAllUIWithStatus 执行完成");
}

#pragma mark – 浮动球

%new
- (void)setupFloatingButton {
    LOGI(@"[悬浮球] setupFloatingButton 方法被调用");
    
    // 获取keyWindow
    UIWindow *w = UIApplication.sharedApplication.keyWindow;
    if (!w) {
        NSArray *windows = UIApplication.sharedApplication.windows;
        if (windows.count > 0) {
            w = windows[0];
        }
    }
    
    if (!w) {
        LOGI(@"[悬浮球] ❌ 无法获取到有效的window");
        return;
    }
    
    // 检查是否已存在悬浮球，如果存在就跳过创建
    UIView *existingFloatingBtn = [w viewWithTag:kFloatingBtnTag];
    if (existingFloatingBtn) {
        LOGI(@"[悬浮球] ⚠️ 悬浮球已存在，跳过创建");
        return;
    }
    
    LOGI(@"[悬浮球] 开始创建悬浮球...");

    // 读取保存的位置
    NSUserDefaults *ud = [NSUserDefaults standardUserDefaults];
    CGFloat x = [ud floatForKey:@"iants_floating_x"];
    CGFloat y = [ud floatForKey:@"iants_floating_y"];
    
    // 获取窗口尺寸
    CGRect windowBounds = w.bounds;
    CGFloat screenWidth = windowBounds.size.width;
    CGFloat screenHeight = windowBounds.size.height;
    
    // 设置默认位置
    if (x <= 0 || x > screenWidth - 60) {
        x = screenWidth - 80;
    }
    if (y <= 0 || y > screenHeight - 95) {
        y = 150;
    }
    
    // 确保在屏幕内
    x = MAX(10, MIN(x, screenWidth - 70));
    y = MAX(50, MIN(y, screenHeight - 100));
    
    // 创建容器视图
    UIView *containerView = [[UIView alloc] initWithFrame:CGRectMake(x, y, 60, 95)];
    containerView.tag = kFloatingBtnTag;
    containerView.userInteractionEnabled = YES;
    [w addSubview:containerView];
    
    // 磨砂白球
    UIVisualEffectView *bubble = [[UIVisualEffectView alloc] initWithEffect:[UIBlurEffect effectWithStyle:UIBlurEffectStyleLight]];
    bubble.frame = CGRectMake(0, 0, 60, 60);
    bubble.layer.cornerRadius = 30;
    bubble.clipsToBounds = YES;
    bubble.backgroundColor = [[UIColor whiteColor] colorWithAlphaComponent:0.5];
    [containerView addSubview:bubble];

    // 状态标签
    UILabel *label = [[UILabel alloc] initWithFrame:bubble.bounds];
    label.tag = 100;
    label.font = [UIFont systemFontOfSize:13 weight:UIFontWeightBold];
    label.textAlignment = NSTextAlignmentCenter;
    [bubble.contentView addSubview:label];
    
    // 序列号标签
    UILabel *serialLabel = [[UILabel alloc] initWithFrame:CGRectMake(0, 65, 65, 20)];
    serialLabel.tag = 101;
    serialLabel.font = [UIFont systemFontOfSize:7 weight:UIFontWeightBold];
    serialLabel.textAlignment = NSTextAlignmentCenter;
    serialLabel.backgroundColor = [[UIColor blackColor] colorWithAlphaComponent:0.5];
    serialLabel.textColor = [UIColor whiteColor];
    serialLabel.layer.cornerRadius = 4;
    serialLabel.clipsToBounds = YES;
    serialLabel.text = getCachedSerialNumber();
    [containerView addSubview:serialLabel];
    
    // 手势识别器
    [containerView addGestureRecognizer:[[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(togglePanel)]];
    [containerView addGestureRecognizer:[[UIPanGestureRecognizer alloc] initWithTarget:self action:@selector(handleDrag:)]];
    UILongPressGestureRecognizer *longPress = [[UILongPressGestureRecognizer alloc] initWithTarget:self action:@selector(handleFloatingButtonLongPress:)];
    longPress.minimumPressDuration = 0.8;
    [containerView addGestureRecognizer:longPress];
    
    // 初始化UI状态
    [self updateAllUIWithStatus];
    
    LOGI(@"[悬浮球] ✅ 悬浮球创建完成");
}

// 拖动悬浮球
%new
- (void)handleDrag:(UIPanGestureRecognizer *)g {
    UIView *v = g.view;
    CGPoint d = [g translationInView:v.superview];
    v.center = CGPointMake(v.center.x + d.x, v.center.y + d.y);
    [g setTranslation:CGPointZero inView:v.superview];
    if (g.state == UIGestureRecognizerStateEnded) {
        CGRect f = v.frame;
        NSUserDefaults *ud = [NSUserDefaults standardUserDefaults];
        [ud setFloat:f.origin.x forKey:@"iants_floating_x"];
        [ud setFloat:f.origin.y forKey:@"iants_floating_y"];
        [ud synchronize];
    }
}

#pragma mark – 面板

%new
- (void)togglePanel {
    // 点击悬浮球时立即检查状态（仅在WebSocket未连接时）
    if (!gWebSocketConnected) {
        [self updateStatusAndUI];
    }
    
    // 获取任务状态（任务状态仍通过HTTP获取）
    dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
        getTaskStatus(nil);
        
        dispatch_async(dispatch_get_main_queue(), ^{
            // 更新任务状态UI
            UIWindow *w = UIApplication.sharedApplication.keyWindow;
            UIView *panel = [w viewWithTag:kPanelTag];
            if (panel) {
                UIView *contentView = [panel.subviews firstObject];
                if (contentView) {
                    UILabel *taskStatusValueLabel = [contentView viewWithTag:106];
                    if (taskStatusValueLabel) {
                        taskStatusValueLabel.text = gTaskStatus;
                    }
                }
            }
        });
    });
    
    dispatch_async(dispatch_get_main_queue(), ^{
        UIWindow *w    = UIApplication.sharedApplication.keyWindow;
        UIView  *old   = [w viewWithTag:kPanelTag];
        UIControl *back = [w viewWithTag:kBackdropTag];

        if (old) {
            // 移除面板
            [old removeFromSuperview];
            [back removeFromSuperview];
            return;
        }

        // 全屏透明遮罩
        UIControl *backdrop = [[UIControl alloc] initWithFrame:w.bounds];
        backdrop.backgroundColor = UIColor.clearColor;
        backdrop.tag = kBackdropTag;
        [backdrop addTarget:self action:@selector(togglePanel) forControlEvents:UIControlEventTouchUpInside];
        [w addSubview:backdrop];
        
        // 面板
        CGFloat P = 30;
        CGFloat panelW = UIScreen.mainScreen.bounds.size.width - 2*P;
        CGFloat panelH = 200;
        CGRect frame = CGRectMake(P,
                                  (UIScreen.mainScreen.bounds.size.height - panelH)/2,
                                  panelW, panelH);
        UIVisualEffectView *panel =
        [[UIVisualEffectView alloc] initWithEffect:
         [UIBlurEffect effectWithStyle:UIBlurEffectStyleSystemMaterialDark]];
        panel.frame = frame;
        panel.layer.cornerRadius = 16;
        panel.clipsToBounds = YES;
        panel.tag = kPanelTag;
        [w addSubview:panel];
        
        UIView *c = [[UIView alloc] initWithFrame:panel.contentView.bounds];
        c.backgroundColor = [[UIColor blackColor] colorWithAlphaComponent:0.4];
        [panel.contentView addSubview:c];
        
        CGFloat m = 15, lh = 28;
        
        // 首行左侧显示序列号，右侧是上报按钮
        CGFloat btnWidth = 70;
        UILabel *serialLabel = [[UILabel alloc] initWithFrame:CGRectMake(m, 6, panelW - 2*m - btnWidth - 10, lh)];
        serialLabel.font = [UIFont systemFontOfSize:16 weight:UIFontWeightBold];
        serialLabel.textColor = UIColor.systemGreenColor;
        serialLabel.text = gIAntsSerialNumber;
        serialLabel.textAlignment = NSTextAlignmentLeft;
        serialLabel.tag = 104; // 方便后续更新
        [c addSubview:serialLabel];
        
        // 添加上报按钮
        UIButton *reportButton = [UIButton buttonWithType:UIButtonTypeSystem];
        reportButton.frame = CGRectMake(panelW - m - btnWidth, 6, btnWidth, lh);
        [reportButton setTitle:@"上报序列号" forState:UIControlStateNormal];
        reportButton.titleLabel.font = [UIFont systemFontOfSize:12 weight:UIFontWeightMedium];
        reportButton.backgroundColor = [UIColor systemBlueColor];
        reportButton.layer.cornerRadius = 6;
        reportButton.tintColor = UIColor.whiteColor;
        [reportButton addTarget:self action:@selector(reportSerialNumber) forControlEvents:UIControlEventTouchUpInside];
        [c addSubview:reportButton];
        
        // 第二行：iAntsCore ON/OFF    右边版本号
        UILabel *left = [[UILabel alloc] initWithFrame:CGRectMake(m, 6 + lh + 4, panelW/2 - m, lh)];
        left.font = [UIFont systemFontOfSize:17 weight:UIFontWeightSemibold];
        left.textColor = UIColor.whiteColor;
        left.text = [NSString stringWithFormat:@"iAntsCore: %@", gIAntsIsOnline?@"🟢":@"🔴"];
        left.tag = 102; // 方便后续更新
        [c addSubview:left];
        
        UILabel *right = [[UILabel alloc] initWithFrame:CGRectMake(panelW/2, 6 + lh + 4, panelW/2 - m, lh)];
        right.font = [UIFont systemFontOfSize:11 weight:UIFontWeightRegular];
        right.textColor = UIColor.lightGrayColor;
        right.textAlignment = NSTextAlignmentRight;
        right.text = gIAntsIsOnline? gIAntsVersion : @"offline";
        right.tag = 103; // 方便后续更新
        [c addSubview:right];
        
        // 任务状态：拆分为两个标签，一个白色标题，一个彩色状态值
        UILabel *taskStatusLabel = [[UILabel alloc] initWithFrame:CGRectMake(m, 6 + lh + 4 + lh + 4, 75, 22)];
        taskStatusLabel.font = [UIFont systemFontOfSize:14 weight:UIFontWeightRegular];
        taskStatusLabel.textColor = UIColor.whiteColor;
        taskStatusLabel.text = @"任务状态:";
        taskStatusLabel.tag = 105;
        [c addSubview:taskStatusLabel];
        
        UILabel *taskStatusValueLabel = [[UILabel alloc] initWithFrame:CGRectMake(m + 75, 6 + lh + 4 + lh + 4, panelW - 2*m - 75, 22)];
        taskStatusValueLabel.font = [UIFont systemFontOfSize:14 weight:UIFontWeightSemibold];
        taskStatusValueLabel.textColor = UIColor.systemCyanColor; // 使用青色作为对比色
        taskStatusValueLabel.text = gTaskStatus;
        taskStatusValueLabel.tag = 106;
        [c addSubview:taskStatusValueLabel];
        
        // 永不锁屏 - 优化对齐
        UILabel *lockLabel = [[UILabel alloc] initWithFrame:CGRectMake(m, 6 + lh + 4 + lh + 4 + 30, 75, 25)];
        lockLabel.font = [UIFont systemFontOfSize:14 weight:UIFontWeightRegular];
        lockLabel.textColor = UIColor.whiteColor;
        lockLabel.text = @"永不锁屏";
        lockLabel.textAlignment = NSTextAlignmentLeft;
        [c addSubview:lockLabel];
        
        UISwitch *lockSwitch = [UISwitch new];
        lockSwitch.transform = CGAffineTransformMakeScale(0.75, 0.75); // 缩小开关尺寸到75%
        CGRect f = lockSwitch.frame;
        f.origin = CGPointMake(m + 75, 6 + lh + 4 + lh + 4 + 30 + 2); // 稍微调整垂直对齐
        lockSwitch.frame = f;
        lockSwitch.tag = 201;
        
        // 永不锁屏开关状态，从NSUserDefaults获取保存的设置
        BOOL preventAutoLock = [[NSUserDefaults standardUserDefaults] boolForKey:@"iants_prevent_autolock"];
        lockSwitch.on = preventAutoLock;
        
        [lockSwitch addTarget:self action:@selector(switchChanged:) forControlEvents:UIControlEventValueChanged];
        [c addSubview:lockSwitch];
        
        // 底部三按钮
        NSArray<UIColor*> *cols = @[
            UIColor.systemOrangeColor,
            UIColor.systemBlueColor,
            UIColor.systemRedColor
        ];
        NSArray<NSString*> *ts = @[@"重启iAntsCore", @"更新iAntsCore", @"重启用户空间"];
        
        // 因为SEL不是Objective-C对象，不能直接用作NSArray泛型参数
        SEL selectors[3] = {
            @selector(restartIAntsCore),
            @selector(updateIAntsCore),
            @selector(restartUserSpace)
        };
        
        CGFloat btnW = (panelW - 2*m - 20)/3;
        for (int i=0; i<3; i++) {
            CGFloat x0 = m + i*(btnW+10);
            UIButton *b = [UIButton buttonWithType:UIButtonTypeCustom];
            b.frame = CGRectMake(x0, panelH - 50, btnW, 36);
            b.layer.cornerRadius = 6;
            b.backgroundColor = cols[i];
            [b setTitle:ts[i] forState:UIControlStateNormal];
            [b setTitleColor:UIColor.whiteColor forState:UIControlStateNormal];
            b.titleLabel.font = [UIFont systemFontOfSize:12 weight:UIFontWeightMedium];
            b.tag = 300 + i;
            [b addTarget:self action:selectors[i] forControlEvents:UIControlEventTouchUpInside];
            [c addSubview:b];
        }
    });
}

%new
- (void)switchChanged:(UISwitch*)sw {
    LOGI(@"switchChanged: %ld, %d", (long)sw.tag, sw.isOn);
    if (sw.tag == 201) {
        // 永不锁屏开关 - 修改为先保存设置再调用函数
        LOGI(@"永不锁屏开关切换: %@", sw.isOn ? @"开启" : @"关闭");
        
        // 1. 先保存到UserDefaults，这样SBPasscode.xm也能读取到最新设置
        [[NSUserDefaults standardUserDefaults] setBool:sw.isOn forKey:@"iants_prevent_autolock"];
        [[NSUserDefaults standardUserDefaults] synchronize];
        
        // 2. 再调用setPreventAutoLock函数应用设置
        setPreventAutoLock(sw.isOn);
        
        // 3. 发送通知，通知其他组件设置已变更
        dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
            CFNotificationCenterPostNotification(CFNotificationCenterGetDarwinNotifyCenter(),
                                             CFSTR("com.iants.preventAutoLock.changed"),
                                             NULL, NULL, YES);
        });
        
        LOGI(@"永不锁屏状态已更新，并已通知其他组件");
    }
}

%new
- (void)restartIAntsCore {
    dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
        // 使用HTTP发送重启请求
        sendRestartAntsRequest();
        LOGI(@"已发送重启iAnts请求");
        
        // 延迟3秒后重新检查状态
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(3.0 * NSEC_PER_SEC)),
                       dispatch_get_main_queue(), ^{
            [self updateStatusAndUI];
        });
    });
    
    // 关闭面板
    [self togglePanel];
}

%new
- (void)updateIAntsCore {
    dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
        // 使用HTTP发送更新请求
        sendUpdateAntsRequest();
        LOGI(@"已发送更新iAnts请求");
        
        // 延迟5秒后重新检查状态
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(5.0 * NSEC_PER_SEC)),
                       dispatch_get_main_queue(), ^{
            [self updateStatusAndUI];
        });
    });
    
    // 关闭面板
    [self togglePanel];
}

%new
- (void)restartUserSpace {
    dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
        sendRebootUserSpaceRequest();
        LOGI(@"已发送重启用户空间请求");
    });
    
    // 关闭面板
    [self togglePanel];
}



// 上报序列号
%new
- (void)reportSerialNumber {
    LOGI(@"[iAntsTouchAbility] 开始上报序列号: %@", gIAntsSerialNumber);
    
    // 关闭面板
    [self togglePanel];
    
    // 在后台线程执行上报
    dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
        // 只调用一次上报请求
        NSDictionary *response = performHTTPRequest(@"/upload/robot_text", @"POST", @{
            @"source": @"UPLOAD",
            @"text": gIAntsSerialNumber
        });
        
        BOOL isSuccess = (response && [[response objectForKey:@"code"] intValue] == 200);
        LOGI(@"[iAntsTouchAbility] 上报结果: %@", isSuccess ? @"成功" : @"失败");
        
        // 回到主线程显示弹窗
        dispatch_async(dispatch_get_main_queue(), ^{
            UIAlertController *alert;
            if (isSuccess) {
                alert = [UIAlertController 
                    alertControllerWithTitle:@"上报成功" 
                    message:[NSString stringWithFormat:@"序列号 %@ 已成功上报", gIAntsSerialNumber]
                    preferredStyle:UIAlertControllerStyleAlert];
            } else {
                alert = [UIAlertController 
                    alertControllerWithTitle:@"上报失败" 
                    message:@"序列号上报失败，请稍后重试"
                    preferredStyle:UIAlertControllerStyleAlert];
            }
            
            [alert addAction:[UIAlertAction 
                actionWithTitle:@"确定" 
                style:UIAlertActionStyleDefault 
                handler:nil]];
            
            // 获取顶层控制器并显示弹窗
            UIViewController *topVC = [self topMostController];
            if (topVC) {
                [topVC presentViewController:alert animated:YES completion:nil];
                LOGI(@"[iAntsTouchAbility] 弹窗已显示");
            } else {
                LOGI(@"[iAntsTouchAbility] 无法获取顶层控制器");
            }
        });
    });
}



// 获取最顶层控制器的方法
%new
- (UIViewController *)topMostController {
    UIWindow *mainWindow = [UIApplication sharedApplication].keyWindow;
    UIViewController *topController = mainWindow.rootViewController;
    
    while (topController.presentedViewController) {
        topController = topController.presentedViewController;
    }
    return topController;
}

// 处理悬浮球长按手势
%new
- (void)handleFloatingButtonLongPress:(UILongPressGestureRecognizer *)gesture {
    if (gesture.state == UIGestureRecognizerStateBegan) {
        UIWindow *w = UIApplication.sharedApplication.keyWindow;
        
        // 检查是否已存在操作菜单，如果存在则先移除
        UIView *existingMenu = [w viewWithTag:10002]; // 使用特定tag标识操作菜单
        if (existingMenu) {
            [existingMenu removeFromSuperview];
        }
        
        // 获取悬浮球位置
        UIView *containerView = [w viewWithTag:kFloatingBtnTag];
        if (!containerView) return;
        
        CGRect floatingBtnFrame = containerView.frame;
        
        // 创建半透明背景
        UIView *menuBackdrop = [[UIView alloc] initWithFrame:w.bounds];
        menuBackdrop.backgroundColor = [[UIColor blackColor] colorWithAlphaComponent:0.3];
        menuBackdrop.tag = 10002;
        [w addSubview:menuBackdrop];
        
        // 计算菜单位置
        CGFloat menuWidth = 120;
        CGFloat menuHeight = 200;
        CGFloat menuX = floatingBtnFrame.origin.x + floatingBtnFrame.size.width + 10;
        CGFloat menuY = floatingBtnFrame.origin.y;
        
        // 边界检查，确保菜单不会超出屏幕
        if (menuX + menuWidth > w.bounds.size.width) {
            menuX = floatingBtnFrame.origin.x - menuWidth - 10; // 显示在左侧
        }
        if (menuY + menuHeight > w.bounds.size.height) {
            menuY = w.bounds.size.height - menuHeight - 50;
        }
        
        // 创建菜单容器
        UIVisualEffectView *menuView = [[UIVisualEffectView alloc] initWithEffect:[UIBlurEffect effectWithStyle:UIBlurEffectStyleSystemMaterialDark]];
        menuView.frame = CGRectMake(menuX, menuY, menuWidth, menuHeight);
        menuView.layer.cornerRadius = 12;
        menuView.clipsToBounds = YES;
        [menuBackdrop addSubview:menuView];
        
        // 菜单项数据
        NSArray *menuItems = @[@"启动任务", @"停止任务", @"关闭任务", @"启动远程"];
        NSArray *menuColors = @[
            [UIColor systemGreenColor],
            [UIColor systemOrangeColor], 
            [UIColor systemRedColor],
            [UIColor systemBlueColor]
        ];
        SEL menuActions[4] = {
            @selector(startTask),
            @selector(stopTask),
            @selector(closeTask),
            @selector(startRemote)
        };
        
        // 创建菜单项
        CGFloat itemHeight = menuHeight / 4;
        for (int i = 0; i < 4; i++) {
            UIButton *menuButton = [UIButton buttonWithType:UIButtonTypeCustom];
            menuButton.frame = CGRectMake(0, i * itemHeight, menuWidth, itemHeight);
            menuButton.backgroundColor = [UIColor clearColor];
            [menuButton setTitle:menuItems[i] forState:UIControlStateNormal];
            [menuButton setTitleColor:menuColors[i] forState:UIControlStateNormal];
            menuButton.titleLabel.font = [UIFont systemFontOfSize:16 weight:UIFontWeightMedium];
            [menuButton addTarget:self action:menuActions[i] forControlEvents:UIControlEventTouchUpInside];
            
            // 添加分隔线（除了最后一个）
            if (i < 3) {
                UIView *separator = [[UIView alloc] initWithFrame:CGRectMake(10, (i + 1) * itemHeight - 0.5, menuWidth - 20, 0.5)];
                separator.backgroundColor = [[UIColor whiteColor] colorWithAlphaComponent:0.2];
                [menuView.contentView addSubview:separator];
            }
            
            [menuView.contentView addSubview:menuButton];
        }
        
        // 点击背景关闭菜单
        UITapGestureRecognizer *tapGesture = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(dismissActionMenu)];
        [menuBackdrop addGestureRecognizer:tapGesture];
        
        // 添加缩放动画
        menuView.transform = CGAffineTransformMakeScale(0.1, 0.1);
        [UIView animateWithDuration:0.2 delay:0 usingSpringWithDamping:0.8 initialSpringVelocity:0.5 options:UIViewAnimationOptionCurveEaseOut animations:^{
            menuView.transform = CGAffineTransformIdentity;
        } completion:nil];
    }
}

// 关闭操作菜单
%new
- (void)dismissActionMenu {
    UIWindow *w = UIApplication.sharedApplication.keyWindow;
    UIView *menuView = [w viewWithTag:10002];
    if (menuView) {
        [UIView animateWithDuration:0.15 animations:^{
            menuView.alpha = 0;
        } completion:^(BOOL finished) {
            [menuView removeFromSuperview];
        }];
    }
}

// 启动任务
%new
- (void)startTask {
    [self dismissActionMenu];
    dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
        NSDictionary *response = performHTTPRequest(@"/task/start", @"POST", @{});
        if (response && [[response objectForKey:@"code"] intValue] == 200) {
            LOGI(@"启动任务请求已发送");
        } else {
            LOGI(@"启动任务请求失败");
        }
        
        // 延迟1秒后更新状态
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.0 * NSEC_PER_SEC)),
                       dispatch_get_main_queue(), ^{
            [self updateStatusAndUI];
        });
    });
}

// 停止任务
%new
- (void)stopTask {
    [self dismissActionMenu];
    dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
        NSDictionary *response = performHTTPRequest(@"/task/stop", @"POST", @{});
        if (response && [[response objectForKey:@"code"] intValue] == 200) {
            LOGI(@"停止任务请求已发送");
        } else {
            LOGI(@"停止任务请求失败");
        }
        
        // 延迟1秒后更新状态
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.0 * NSEC_PER_SEC)),
                       dispatch_get_main_queue(), ^{
            [self updateStatusAndUI];
        });
    });
}

// 关闭任务
%new
- (void)closeTask {
    [self dismissActionMenu];
    dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
        NSDictionary *response = performHTTPRequest(@"/task/close", @"POST", @{});
        if (response && [[response objectForKey:@"code"] intValue] == 200) {
            LOGI(@"关闭任务请求已发送");
        } else {
            LOGI(@"关闭任务请求失败");
        }
        
        // 延迟1秒后更新状态
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.0 * NSEC_PER_SEC)),
                       dispatch_get_main_queue(), ^{
            [self updateStatusAndUI];
        });
    });
}

// 启动远程
%new
- (void)startRemote {
    [self dismissActionMenu];
    dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
        NSDictionary *response = performHTTPRequest(@"/task/remote", @"POST", @{});
        if (response && [[response objectForKey:@"code"] intValue] == 200) {
            LOGI(@"启动远程请求已发送");
        } else {
            LOGI(@"启动远程请求失败");
        }
        
        // 延迟1秒后更新状态
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.0 * NSEC_PER_SEC)),
                       dispatch_get_main_queue(), ^{
            [self updateStatusAndUI];
        });
    });
}

%end
