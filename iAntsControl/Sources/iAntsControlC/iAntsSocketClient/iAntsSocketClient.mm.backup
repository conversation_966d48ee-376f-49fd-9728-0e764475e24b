//
//  iAntsSocketClient.mm
//  iAnts WebSocket Client
//
//  Created by iAnts on 2024/01/01.
//  Copyright © 2024 iAnts. All rights reserved.
//

#import "iAntsSocketClient.h"

// MARK: - IAntsSocketConfig Implementation

@implementation IAntsSocketConfig

// 移除defaultConfig方法 - 要求调用者必须提供所有配置参数

- (instancetype)initWithServerURL:(NSString *)serverURL
              maxReconnectAttempts:(NSInteger)maxReconnectAttempts
              autoReconnectEnabled:(BOOL)autoReconnectEnabled
                   heartbeatEnabled:(BOOL)heartbeatEnabled
                  heartbeatInterval:(NSTimeInterval)heartbeatInterval {
    // 严格参数验证 - 调用者必须提供有效参数
    if (!serverURL || serverURL.length == 0) {
        LOGE(@"[IAntsSocketConfig] 错误：serverURL不能为空");
        return nil;
    }
    
    if (![serverURL hasPrefix:@"ws://"] && ![serverURL hasPrefix:@"wss://"]) {
        LOGE(@"[IAntsSocketConfig] 错误：serverURL必须以ws://或wss://开头");
        return nil;
    }
    
    if (maxReconnectAttempts < 0) {
        LOGE(@"[IAntsSocketConfig] 错误：maxReconnectAttempts不能为负数");
        return nil;
    }
    
    if (heartbeatEnabled && heartbeatInterval <= 0) {
        LOGE(@"[IAntsSocketConfig] 错误：启用心跳时heartbeatInterval必须大于0");
        return nil;
    }
    
    self = [super init];
    if (self) {
        _serverURL = [serverURL copy];
        _maxReconnectAttempts = maxReconnectAttempts;
        _autoReconnectEnabled = autoReconnectEnabled;
        _heartbeatEnabled = heartbeatEnabled;
        _heartbeatInterval = heartbeatInterval;
        
        LOGI(@"[IAntsSocketConfig] ✅ 配置创建成功 - 服务器: %@", serverURL);
    }
    return self;
}

@end

// MARK: - IAntsSocketClient Implementation

@interface IAntsSocketClient () <NSURLSessionWebSocketDelegate, NSURLSessionDelegate>

// MARK: - Private Properties

/// WebSocket任务
@property (nonatomic, strong, nullable) NSURLSessionWebSocketTask *webSocketTask;

/// URL会话
@property (nonatomic, strong, nullable) NSURLSession *urlSession;

/// 是否已连接
@property (nonatomic, assign) BOOL isConnected;

/// 是否应该重连
@property (nonatomic, assign) BOOL shouldReconnect;

/// 重连尝试次数
@property (nonatomic, assign) NSInteger reconnectAttempts;

/// 重连定时器
@property (nonatomic, strong, nullable) NSTimer *reconnectTimer;

/// 心跳定时器
@property (nonatomic, strong, nullable) NSTimer *heartbeatTimer;

/// 心跳线程
@property (nonatomic, strong, nullable) NSThread *heartbeatThread;

/// 客户端配置
@property (nonatomic, strong, readwrite) IAntsSocketConfig *config;

/// 消息处理器字典
@property (nonatomic, strong, nonnull) NSMutableDictionary<NSNumber *, IAntsMessageHandler> *messageHandlers;

// 移除os_log_t，使用common/LogC.h的日志功能

@end

@implementation IAntsSocketClient

// MARK: - Singleton & Initialization

+ (instancetype)shared {
    static IAntsSocketClient *sharedInstance = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        sharedInstance = [[self alloc] init];
    });
    return sharedInstance;
}

- (instancetype)init {
    self = [super init];
    if (self) {
        // 初始化状态变量
        _isConnected = NO;
        _shouldReconnect = NO;
        _reconnectAttempts = 0;
        
        // 初始化心跳相关变量
        _heartbeatTimer = nil;
        _heartbeatThread = nil;
        
        // 初始化消息处理器字典
        _messageHandlers = [[NSMutableDictionary alloc] init];
        
        LOGI(@"[iAntsSocketClient] 初始化WebSocket客户端");
    }
    return self;
}

- (void)dealloc {
    [self disconnect];
}

// MARK: - Public Methods

- (BOOL)initializeAndConnectWithConfig:(IAntsSocketConfig *)config {
    // 验证配置
    if (!config || !config.serverURL || config.serverURL.length == 0) {
        LOGE(@"[iAntsSocketClient] 无效的配置参数");
        return NO;
    }
    
    // 验证服务器URL
    NSURL *url = [NSURL URLWithString:config.serverURL];
    if (!url) {
        LOGE(@"[iAntsSocketClient] 无效的服务器URL: %@", config.serverURL);
        return NO;
    }
    
    // 保存配置
    self.config = config;
    
    // 设置URL会话
    [self setupURLSession];
    
    LOGI(@"[iAntsSocketClient] 配置已更新 - 服务器: %@", config.serverURL);
    LOGI(@"[iAntsSocketClient] 最大重连次数: %ld, 自动重连: %@, 心跳: %@", 
         (long)config.maxReconnectAttempts,
         config.autoReconnectEnabled ? @"启用" : @"禁用",
         config.heartbeatEnabled ? @"启用" : @"禁用");
    
    // 立即连接
    return [self connect];
}

- (BOOL)connect {
    if (!self.config) {
        LOGE(@"[iAntsSocketClient] 请先调用initializeAndConnectWithConfig:方法进行配置");
        return NO;
    }
    
    // 检查是否已经连接
    if (self.webSocketTask != nil && self.isConnected) {
        LOGI(@"[iAntsSocketClient] WebSocket已经连接");
        return YES;
    }
    
    // 清理旧的连接
    if (self.webSocketTask != nil) {
        LOGI(@"[iAntsSocketClient] 清理旧的WebSocket连接");
        [self.webSocketTask cancelWithCloseCode:NSURLSessionWebSocketCloseCodeGoingAway reason:nil];
        self.webSocketTask = nil;
    }
    
    self.shouldReconnect = self.config.autoReconnectEnabled;
    
    // 创建WebSocket任务
    NSURL *serverURL = [NSURL URLWithString:self.config.serverURL];
    NSURLRequest *request = [NSURLRequest requestWithURL:serverURL];
    self.webSocketTask = [self.urlSession webSocketTaskWithRequest:request];
    
    if (!self.webSocketTask) {
        LOGE(@"[iAntsSocketClient] 无法创建WebSocket任务");
        return NO;
    }
    
    LOGI(@"[iAntsSocketClient] 开始连接到 %@ (尝试次数: %ld)", 
         serverURL.absoluteString, (long)(self.reconnectAttempts + 1));
    
    // 启动任务并开始接收消息
    [self.webSocketTask resume];
    [self startReceiving];
    
    return YES;
}

- (void)disconnect {
    self.shouldReconnect = NO;
    [self stopReconnecting];
    [self stopHeartbeat];
    
    if (self.webSocketTask) {
        LOGI(@"[iAntsSocketClient] 断开WebSocket连接");
        [self.webSocketTask cancelWithCloseCode:NSURLSessionWebSocketCloseCodeNormalClosure reason:nil];
        self.webSocketTask = nil;
    }
    
    self.isConnected = NO;
}

- (BOOL)sendMessage:(IAntsMessage *)message {
    if (!message) {
        LOGE(@"[iAntsSocketClient] 消息对象为空");
        return NO;
    }
    
    // 序列化消息
    NSError *error = nil;
    NSData *data = [message serializeToData:&error];
    if (!data) {
        LOGE(@"[iAntsSocketClient] 消息序列化失败: %@", error.localizedDescription);
        return NO;
    }
    
    return [self sendRawData:data messageType:message.type content:message.content];
}

- (BOOL)sendMessageType:(IAntsMessageType *)messageType content:(NSDictionary *)content {
    // 使用IAntsProtobuf进行序列化
    NSError *error = nil;
    NSData *data = [[IAntsProtobuf shared] serializeMessageType:messageType content:content error:&error];
    if (!data) {
        LOGE(@"[iAntsSocketClient] 消息序列化失败: %@", error.localizedDescription);
        return NO;
    }
    
    return [self sendRawData:data messageType:messageType content:content];
}

- (BOOL)sendHeartbeatWithVersion:(NSString *)version {
    NSDictionary *content = @{@"version": version ?: @"1.0.0"};
    // 使用类方法调用替代枚举常量
    IAntsMessageType *heartbeatType = [IAntsMessageType commonMessageType:IAntsCommonMessageTypeHeartbeat];
    LOGI(@"[iAntsSocketClient] 构造心跳消息: 类型=%@, 内容=%@", heartbeatType, content);
    BOOL result = [self sendMessageType:heartbeatType content:content];
    LOGI(@"[iAntsSocketClient] 心跳消息发送调用结果: %@", result ? @"成功" : @"失败");
    return result;
}

- (void)registerHandlerForMessageType:(IAntsMessageType *)messageType handler:(IAntsMessageHandler)handler {
    if (!handler) {
        LOGE(@"[iAntsSocketClient] 消息处理器不能为空");
        return;
    }
    
    // 使用description方法而不是boxed expression
    NSString *key = [messageType description];
    self.messageHandlers[key] = [handler copy];
    LOGI(@"[iAntsSocketClient] 注册消息处理器: 类型=%@", messageType);
}

- (void)removeHandlerForMessageType:(IAntsMessageType *)messageType {
    // 使用description方法而不是boxed expression
    NSString *key = [messageType description];
    [self.messageHandlers removeObjectForKey:key];
    LOGI(@"[iAntsSocketClient] 移除消息处理器: 类型=%@", messageType);
}

- (void)setAutoReconnectEnabled:(BOOL)enabled {
    if (self.config) {
        // 创建新的配置对象（因为config的属性是readonly）
        self.config = [[IAntsSocketConfig alloc] initWithServerURL:self.config.serverURL
                                              maxReconnectAttempts:self.config.maxReconnectAttempts
                                              autoReconnectEnabled:enabled
                                                   heartbeatEnabled:self.config.heartbeatEnabled
                                                  heartbeatInterval:self.config.heartbeatInterval];
    }
    
    self.shouldReconnect = enabled;
    LOGI(@"[iAntsSocketClient] 自动重连已%@", enabled ? @"启用" : @"禁用");
    
    if (!enabled) {
        [self stopReconnecting];
    }
}

- (void)setHeartbeatEnabled:(BOOL)enabled {
    if (self.config) {
        // 创建新的配置对象
        self.config = [[IAntsSocketConfig alloc] initWithServerURL:self.config.serverURL
                                              maxReconnectAttempts:self.config.maxReconnectAttempts
                                              autoReconnectEnabled:self.config.autoReconnectEnabled
                                                   heartbeatEnabled:enabled
                                                  heartbeatInterval:self.config.heartbeatInterval];
    }
    
    if (enabled && self.isConnected) {
        LOGI(@"[iAntsSocketClient] 动态启用心跳机制");
        [self startHeartbeat];
    } else {
        LOGI(@"[iAntsSocketClient] 动态禁用心跳机制");
        [self stopHeartbeat];
    }
    
    LOGI(@"[iAntsSocketClient] 心跳机制已%@", enabled ? @"启用" : @"禁用");
}

- (BOOL)connected {
    return self.isConnected;
}

- (void)reset {
    [self disconnect];
    [self.messageHandlers removeAllObjects];
    self.reconnectAttempts = 0;
    LOGI(@"[iAntsSocketClient] 客户端状态已重置");
}

// MARK: - Private Methods

- (BOOL)sendRawData:(NSData *)data messageType:(IAntsMessageType *)messageType content:(NSDictionary *)content {
    // 检查WebSocket任务是否存在且处于运行状态
    if (!self.webSocketTask || self.webSocketTask.state != NSURLSessionTaskStateRunning) {
        LOGE(@"[iAntsSocketClient] WebSocket任务不可用(状态: %ld)，无法发送消息", 
             (long)(self.webSocketTask ? self.webSocketTask.state : -1));
        return NO;
    }
    
    // 通知委托发送前回调
    if (self.delegate && [self.delegate respondsToSelector:@selector(webSocketWillSendMessage:content:)]) {
        [self.delegate webSocketWillSendMessage:messageType content:content];
    }
    
    // 创建WebSocket消息并发送
    NSURLSessionWebSocketMessage *websocketMessage = 
        [[NSURLSessionWebSocketMessage alloc] initWithData:data];
    
    __weak IAntsSocketClient *weakSelf = self;
    [self.webSocketTask sendMessage:websocketMessage completionHandler:^(NSError * _Nullable error) {
        __strong IAntsSocketClient *strongSelf = weakSelf;
        if (!strongSelf) return;
        
        if (error) {
            LOGE(@"[iAntsSocketClient] 发送消息失败: %@", error.localizedDescription);
            
            // 通知委托发送结果
            dispatch_async(dispatch_get_main_queue(), ^{
                if (strongSelf.delegate && [strongSelf.delegate respondsToSelector:@selector(webSocketDidSendMessage:success:error:)]) {
                    [strongSelf.delegate webSocketDidSendMessage:messageType success:NO error:error];
                }
            });
            
            // 检查是否是网络连接问题
            if ([error.domain isEqualToString:NSURLErrorDomain] && 
                (error.code == NSURLErrorNetworkConnectionLost || 
                 error.code == NSURLErrorNotConnectedToInternet)) {
                LOGI(@"[iAntsSocketClient] 检测到网络连接问题，触发重连");
                [strongSelf handleConnectionError:error];
            }
        } else {
            LOGI(@"[iAntsSocketClient] 消息发送成功，类型: %@", messageType);
            
            // 通知委托发送结果
            dispatch_async(dispatch_get_main_queue(), ^{
                if (strongSelf.delegate && [strongSelf.delegate respondsToSelector:@selector(webSocketDidSendMessage:success:error:)]) {
                    [strongSelf.delegate webSocketDidSendMessage:messageType success:YES error:nil];
                }
            });
        }
    }];
    
    return YES;
}

- (void)setupURLSession {
    // 配置URL会话
    NSURLSessionConfiguration *configuration = [NSURLSessionConfiguration defaultSessionConfiguration];
    configuration.timeoutIntervalForRequest = 120.0;  // 120秒请求超时
    configuration.timeoutIntervalForResource = 0;     // 无限资源超时
    
    // 创建后台队列处理WebSocket回调
    NSOperationQueue *backgroundQueue = [[NSOperationQueue alloc] init];
    backgroundQueue.name = @"com.iants.websocket.delegate";
    backgroundQueue.maxConcurrentOperationCount = 1;
    backgroundQueue.qualityOfService = NSQualityOfServiceUtility;
    
    self.urlSession = [NSURLSession sessionWithConfiguration:configuration
                                                    delegate:self
                                               delegateQueue:backgroundQueue];
}

- (void)startReceiving {
    if (!self.webSocketTask) return;
    
    LOGI(@"[iAntsSocketClient] 🎯 启动消息接收等待...");
    
    __weak IAntsSocketClient *weakSelf = self;
    [self.webSocketTask receiveMessageWithCompletionHandler:^(NSURLSessionWebSocketMessage * _Nullable message, NSError * _Nullable error) {
        __strong IAntsSocketClient *strongSelf = weakSelf;
        if (!strongSelf) return;
        
        if (message) {
            [strongSelf handleReceivedMessage:message];
            
            // 🔧 修复：使用WebSocket任务的实际状态而不是isConnected标志
            // 继续接收下一个消息，只要WebSocket任务仍在运行状态
            if (strongSelf.webSocketTask && strongSelf.webSocketTask.state == NSURLSessionTaskStateRunning) {
                LOGI(@"[iAntsSocketClient] 📨 消息处理完成，继续接收下一个消息");
                [strongSelf startReceiving];
            } else {
                LOGW(@"[iAntsSocketClient] ⚠️ WebSocket任务状态异常(状态:%ld)，停止接收消息", 
                     (long)(strongSelf.webSocketTask ? strongSelf.webSocketTask.state : -1));
            }
        } else if (error) {
            LOGE(@"[iAntsSocketClient] 接收消息失败: %@", error.localizedDescription);
            [strongSelf handleConnectionError:error];
        }
    }];
}

- (void)handleReceivedMessage:(NSURLSessionWebSocketMessage *)websocketMessage {
    LOGI(@"[iAntsSocketClient] 🔄 开始处理收到的WebSocket消息");
    
    switch (websocketMessage.type) {
        case NSURLSessionWebSocketMessageTypeData: {
            NSData *data = websocketMessage.data;
            LOGI(@"[iAntsSocketClient] 收到二进制数据，长度: %lu", (unsigned long)data.length);
            
            // 使用IAntsProtobuf反序列化消息
            NSError *parseError = nil;
            NSDictionary *result = [[IAntsProtobuf shared] deserializeData:data error:&parseError];
            if (!result) {
                LOGE(@"[iAntsSocketClient] 消息反序列化失败: %@", parseError.localizedDescription);
                LOGI(@"[iAntsSocketClient] ❌ 消息处理完成（反序列化失败）");
                return;
            }
            
            // 修正对象类型的获取方式
            IAntsMessageType *messageType = result[@"type"];
            NSDictionary *content = result[@"content"];
            
            LOGI(@"[iAntsSocketClient] 消息反序列化成功，类型: %@", messageType);
            LOGI(@"[iAntsSocketClient] 消息内容: %@", content);
            
            // 特别记录心跳消息
            IAntsMessageType *heartbeatType = [IAntsMessageType commonMessageType:IAntsCommonMessageTypeHeartbeat];
            if ([messageType isEqual:heartbeatType]) {
                LOGI(@"[iAntsSocketClient] 🟢 收到心跳消息，准备调用处理器");
            }
            
            // 调用消息处理器
            NSString *key = [messageType description];
            IAntsMessageHandler handler = self.messageHandlers[key];
            if (handler) {
                LOGI(@"[iAntsSocketClient] 找到消息处理器，类型: %@", messageType);
                dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
                    handler(messageType, content);
                });
            } else {
                LOGI(@"[iAntsSocketClient] ❌ 未找到消息处理器: 类型=%@", messageType);
                LOGI(@"[iAntsSocketClient] 当前已注册的处理器数量: %lu", (unsigned long)self.messageHandlers.count);
            }
            
            // 在主线程通知委托
            dispatch_async(dispatch_get_main_queue(), ^{
                if (self.delegate && [self.delegate respondsToSelector:@selector(webSocketDidReceiveMessage:content:)]) {
                    [self.delegate webSocketDidReceiveMessage:messageType content:content];
                }
            });
            
            break;
        }
        case NSURLSessionWebSocketMessageTypeString: {
            NSString *text = websocketMessage.string;
            LOGI(@"[iAntsSocketClient] 收到文本消息: %@", text);
            break;
        }
        default:
            LOGI(@"[iAntsSocketClient] 收到未知类型的消息");
            break;
    }
    
    LOGI(@"[iAntsSocketClient] ✅ 消息处理完成，准备继续接收下一个消息");
}

- (void)handleConnectionError:(NSError *)error {
    self.isConnected = NO;
    
    // 停止心跳（现在在独立线程中，不需要主线程调度）
    [self stopHeartbeat];
    
    // 记录详细错误信息
    LOGE(@"[iAntsSocketClient] 连接错误: 代码=%ld, 域=%@, 描述=%@", 
         (long)error.code, error.domain, error.localizedDescription);
    
    // 在后台线程通知委托（避免阻塞主线程）
    dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
        if (self.delegate && [self.delegate respondsToSelector:@selector(webSocketDidDisconnect:)]) {
            [self.delegate webSocketDidDisconnect:error];
        }
    });
    
    // 如果需要重连，安排重连
    if (self.shouldReconnect) {
        [self scheduleReconnect];
    }
}

- (void)scheduleReconnect {
    if (!self.shouldReconnect || !self.config) return;
    
    self.reconnectAttempts++;
    
    // 检查重连次数限制
    if (self.reconnectAttempts > self.config.maxReconnectAttempts) {
        LOGI(@"[iAntsSocketClient] 重连次数超过限制 (%ld)，停止重连", 
             (long)self.config.maxReconnectAttempts);
        [self stopReconnecting];
        return;
    }
    
    // 计算重连延迟（指数退避，最大120秒）
    NSTimeInterval delay = MIN(pow(2.0, self.reconnectAttempts - 1) * 5.0, 120.0);
    LOGI(@"[iAntsSocketClient] 安排重连，延迟 %.1f 秒 (第 %ld 次尝试)", 
         delay, (long)self.reconnectAttempts);
    
    // 在主线程创建重连定时器
    dispatch_async(dispatch_get_main_queue(), ^{
        [self.reconnectTimer invalidate];
        self.reconnectTimer = [NSTimer scheduledTimerWithTimeInterval:delay
                                                               target:self
                                                             selector:@selector(performReconnect)
                                                             userInfo:nil
                                                              repeats:NO];
    });
}

- (void)performReconnect {
    LOGI(@"[iAntsSocketClient] 执行重连...");
    
    // 清理当前连接
    [self.webSocketTask cancel];
    self.webSocketTask = nil;
    self.isConnected = NO;
    
    // 重新连接
    [self connect];
}

- (void)stopReconnecting {
    self.shouldReconnect = NO;
    self.reconnectAttempts = 0;
    
    // 在主线程取消重连定时器
    dispatch_async(dispatch_get_main_queue(), ^{
        [self.reconnectTimer invalidate];
        self.reconnectTimer = nil;
    });
}

- (void)startHeartbeat {
    if (!self.config || !self.config.heartbeatEnabled) return;
    
    // 先停止现有的心跳
    [self stopHeartbeat];
    
    // 使用配置中的心跳间隔，不做限制
    NSTimeInterval heartbeatInterval = self.config.heartbeatInterval;
    LOGI(@"[iAntsSocketClient] 启动心跳线程，每%.1f秒发送一次", heartbeatInterval);
    
    // 创建专门的心跳线程
    self.heartbeatThread = [[NSThread alloc] initWithTarget:self 
                                                   selector:@selector(heartbeatThreadMain:)
                                                     object:@(heartbeatInterval)];
    self.heartbeatThread.name = @"com.iants.websocket.heartbeat";
    [self.heartbeatThread start];
    
    LOGI(@"[iAntsSocketClient] 心跳线程已启动: %@", self.heartbeatThread.name);
    
    // 延迟0.5秒后发送初始心跳，确保WebSocket完全就绪
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), 
                   dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
        [self sendInitialHeartbeat];
    });
    
    LOGI(@"[iAntsSocketClient] 初始心跳将在0.5秒后发送，定时心跳将在%.1f秒后开始", heartbeatInterval);
}

// 心跳线程主函数
- (void)heartbeatThreadMain:(NSNumber *)heartbeatIntervalNumber {
    @autoreleasepool {
        NSTimeInterval heartbeatInterval = [heartbeatIntervalNumber doubleValue];
        LOGI(@"[iAntsSocketClient] 心跳线程开始运行，间隔: %.1f秒", heartbeatInterval);
        
        // 为这个线程创建RunLoop
        NSRunLoop *runLoop = [NSRunLoop currentRunLoop];
        
        // 创建心跳定时器
        self.heartbeatTimer = [NSTimer scheduledTimerWithTimeInterval:heartbeatInterval
                                                               target:self
                                                             selector:@selector(sendHeartbeatTimerFired)
                                                             userInfo:nil
                                                              repeats:YES];
        
        LOGI(@"[iAntsSocketClient] 心跳定时器已创建，开始运行 RunLoop");
        
        // 运行RunLoop，直到线程被取消
        while (![[NSThread currentThread] isCancelled]) {
            @autoreleasepool {
                // 运行RunLoop，每次最多运行0.5秒
                [runLoop runMode:NSDefaultRunLoopMode beforeDate:[NSDate dateWithTimeIntervalSinceNow:0.5]];
            }
        }
        
        // 线程即将结束，清理定时器
        [self.heartbeatTimer invalidate];
        self.heartbeatTimer = nil;
        
        LOGI(@"[iAntsSocketClient] 心跳线程已结束");
    }
}

- (void)stopHeartbeat {
    LOGI(@"[iAntsSocketClient] 停止心跳机制");
    
    // 停止心跳定时器
    if (self.heartbeatTimer) {
        [self.heartbeatTimer invalidate];
        self.heartbeatTimer = nil;
    }
    
    // 停止心跳线程
    if (self.heartbeatThread && !self.heartbeatThread.isCancelled) {
        LOGI(@"[iAntsSocketClient] 正在停止心跳线程: %@", self.heartbeatThread.name);
        [self.heartbeatThread cancel];
        
        // 等待线程结束（最多等待2秒）
        NSDate *timeoutDate = [NSDate dateWithTimeIntervalSinceNow:2.0];
        while (self.heartbeatThread.isExecuting && [[NSDate date] compare:timeoutDate] == NSOrderedAscending) {
            [[NSRunLoop currentRunLoop] runMode:NSDefaultRunLoopMode beforeDate:[NSDate dateWithTimeIntervalSinceNow:0.1]];
        }
        
        self.heartbeatThread = nil;
        LOGI(@"[iAntsSocketClient] 心跳线程已停止");
    }
}

// 发送初始心跳（连接成功后立即调用）
- (void)sendInitialHeartbeat {
    LOGI(@"[iAntsSocketClient] 🟢 发送初始心跳");
    BOOL heartbeatSent = [self sendHeartbeatWithVersion:@"iAntsCT-1.0.0"];
    LOGI(@"[iAntsSocketClient] 初始心跳发送结果: %@", heartbeatSent ? @"成功" : @"失败");
}

- (void)sendHeartbeatTimerFired {
    // 检查WebSocket任务的实际状态，而不是依赖isConnected标志
    if (self.webSocketTask && self.webSocketTask.state == NSURLSessionTaskStateRunning) {
        // 发送心跳并检查结果
        LOGI(@"[iAntsSocketClient] 🟡 准备发送定时心跳到服务端");
        BOOL heartbeatSent = [self sendHeartbeatWithVersion:@"iAntsCT-1.0.0"];
        LOGI(@"[iAntsSocketClient] 定时心跳发送结果: %@", heartbeatSent ? @"成功" : @"失败");
        
        // 如果发送失败，记录但不立即断开连接，让WebSocket自然检测断开
        if (!heartbeatSent) {
            LOGW(@"[iAntsSocketClient] 心跳发送失败，等待WebSocket委托处理连接状态");
        }
    } else {
        LOGW(@"[iAntsSocketClient] WebSocket任务不可用或已停止，跳过本次心跳");
        // 不立即停止心跳，让WebSocket委托方法处理断开连接
    }
}

// MARK: - NSURLSessionWebSocketDelegate

- (void)URLSession:(NSURLSession *)session 
      webSocketTask:(NSURLSessionWebSocketTask *)webSocketTask 
  didOpenWithProtocol:(NSString *)protocol {
    LOGI(@"[iAntsSocketClient] WebSocket连接成功，协议: %@", protocol ?: @"无");
    self.isConnected = YES;
    self.reconnectAttempts = 0;
    
    // 在主线程清理重连定时器
    dispatch_async(dispatch_get_main_queue(), ^{
        [self.reconnectTimer invalidate];
        self.reconnectTimer = nil;
    });
    
    // 在后台线程启动心跳机制
    dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
        [self startHeartbeat];
    });
    
    // 在后台线程通知委托（避免阻塞主线程）
    dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
        if (self.delegate && [self.delegate respondsToSelector:@selector(webSocketDidConnect)]) {
            [self.delegate webSocketDidConnect];
        }
    });
}

- (void)URLSession:(NSURLSession *)session 
      webSocketTask:(NSURLSessionWebSocketTask *)webSocketTask 
       didCloseWithCode:(NSURLSessionWebSocketCloseCode)closeCode 
             reason:(NSData *)reason {
    
    // 解析关闭原因
    NSString *reasonString = @"无原因";
    if (reason) {
        NSString *decoded = [[NSString alloc] initWithData:reason encoding:NSUTF8StringEncoding];
        if (decoded) {
            reasonString = decoded;
        }
    }
    
    LOGI(@"[iAntsSocketClient] WebSocket连接关闭，代码: %ld, 原因: %@", 
         (long)closeCode, reasonString);
    
    self.isConnected = NO;
    
    // 停止心跳（现在在独立线程中，不需要主线程调度）
    [self stopHeartbeat];
    
    // 根据关闭代码决定是否重连
    BOOL shouldAttemptReconnect = YES;
    switch (closeCode) {
        case NSURLSessionWebSocketCloseCodeNormalClosure:
            shouldAttemptReconnect = NO;
            break;
        case NSURLSessionWebSocketCloseCodeGoingAway:
        case NSURLSessionWebSocketCloseCodeProtocolError:
        case NSURLSessionWebSocketCloseCodeUnsupportedData:
        case NSURLSessionWebSocketCloseCodeInvalidFramePayloadData:
        case NSURLSessionWebSocketCloseCodePolicyViolation:
        case NSURLSessionWebSocketCloseCodeMessageTooBig:
        case NSURLSessionWebSocketCloseCodeInternalServerError:
        case NSURLSessionWebSocketCloseCodeAbnormalClosure:
            shouldAttemptReconnect = YES;
            break;
        default:
            shouldAttemptReconnect = YES;
            break;
    }
    
    // 创建错误对象
    NSError *error = [NSError errorWithDomain:@"iAntsWebSocket" 
                                        code:closeCode 
                                    userInfo:@{NSLocalizedDescriptionKey: [NSString stringWithFormat:@"WebSocket连接关闭: %@", reasonString]}];
    
    // 在后台线程通知委托（避免阻塞主线程）
    dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
        if (self.delegate && [self.delegate respondsToSelector:@selector(webSocketDidDisconnect:)]) {
            [self.delegate webSocketDidDisconnect:error];
        }
    });
    
    // 如果需要且应该重连，则安排重连
    if (shouldAttemptReconnect && self.shouldReconnect) {
        LOGI(@"[iAntsSocketClient] 连接断开，准备重连...");
        [self scheduleReconnect];
    }
}

// MARK: - NSURLSessionDelegate

- (void)URLSession:(NSURLSession *)session 
              task:(NSURLSessionTask *)task 
didCompleteWithError:(NSError *)error {
    if (error) {
        LOGE(@"[iAntsSocketClient] Session任务完成时出错: 代码=%ld, 域=%@, 描述=%@", 
             (long)error.code, error.domain, error.localizedDescription);
        
        // 如果是当前的WebSocket任务出错，处理连接错误
        if (task == self.webSocketTask) {
            [self handleConnectionError:error];
        }
    } else {
        LOGI(@"[iAntsSocketClient] Session任务正常完成");
    }
}

@end