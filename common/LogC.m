//
//  Logger.m
//  iAntsCore
//
//  Created by sun on 2024/10/18.
//

#import "LogC.h"

void LogMessage(LogLevel level, const char *file, int line, const char *function, NSString *format, ...) {
    // 检测环境：只有在 DEBUG 模式下才打印 DEBUG 信息
#ifndef DEBUG
    if (level == LogLevelDebug) {
        return; // 非 DEBUG 环境下，直接返回，不打印 DEBUG 信息
    }
#endif
    
    // 定义不同级别的日志标签
    NSString *levelString;
    switch (level) {
        case LogLevelError:
            levelString = @"ERROR";
            break;
        case LogLevelWarning:
            levelString = @"WARNING";
            break;
        case LogLevelInfo:
            levelString = @"INFO";
            break;
        case LogLevelDebug:
            levelString = @"DEBUG";
            break;
        default:
            levelString = @"UNKNOWN";
            break;
    }
    
    // 获取可变参数列表
    va_list args;
    va_start(args, format);
    NSString *message = [[NSString alloc] initWithFormat:format arguments:args];
    va_end(args);
    
    // 获取日志输出的文件名（去掉路径）
    NSString *fileName = [[NSString stringWithUTF8String:file] lastPathComponent];
    
    // 获取当前时间字符串
    NSDate *now = [NSDate date];
    NSDateFormatter *dateFormatter = [[NSDateFormatter alloc] init];
    [dateFormatter setDateFormat:@"yyyy-MM-dd HH:mm:ss.SSS"];
    NSString *timestamp = [dateFormatter stringFromDate:now];
    
    // 格式化日志输出
    NSLog(@"[%@][iAnts][com.iants.control][%@] %s:%d [%s] %@", timestamp, levelString, fileName.UTF8String, line, function, message);
}

