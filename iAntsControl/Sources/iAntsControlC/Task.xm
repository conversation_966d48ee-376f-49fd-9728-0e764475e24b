#import "Task.h"
#import "./handlers/Touch.h"
#import "./handlers/Process.h"
#import "./handlers/AlertBox.h"
#import "./handlers/Record.h"
#import "./handlers/Toast.h"
#import "./handlers/UIKeyboard.h"
#import "./handlers/DeviceInfo.h"
#import "TouchIndicator/TouchIndicatorWindow.h"
#import <mach/mach.h>
#import <Foundation/NSDistributedNotificationCenter.h>
#import <TextRecognization/TextRecognizer.h>
#import "./handlers/Screen.h"
#import "NSTask.h"
#import "./handlers/ScreenShot.h"
#import "./server/SocketServer.h"
#import "Common.h"
#import <spawn.h>

extern CFRunLoopRef recordRunLoop;

/*
get task type
*/
static int getTaskType(UInt8* dataArray)
{
	int taskType = 0;
	for (int i = 0; i <= 1; i++)
	{
		taskType += (dataArray[i] - '0')*pow(10, 1-i);
	}
	return taskType;
}

/**
Process Task
*/
void processTask(UInt8 *buff, CFWriteStreamRef writeStreamRef)
{
    @autoreleasepool {
        // NSLog(@"[iAntsControlAbility] task type: %d. Data: %s", getTaskType(buff), buff);
        UInt8 *eventData = buff + 0x2;
        int taskType = getTaskType(buff);

        //for touching
        if (taskType == TASK_PERFORM_TOUCH)
        {
            performTouchFromRawData(eventData);
        }
        else if (taskType == TASK_PROCESS_BRING_FOREGROUND) //bring to foreground
        {      
            switchProcessForegroundFromRawData(eventData);
            // notifyClient((UInt8*)"0\r\n", writeStreamRef); 
        }
        else if (taskType == TASK_SHOW_ALERT_BOX)
        { 
            NSError *err = nil;
            showAlertBoxFromRawData(eventData, &err);
            if (err)
            {
                NSLog(@"[iAntsControlAbility] show alert box error: %@", [err localizedDescription]);
                // notifyClient((UInt8*)[[err localizedDescription] UTF8String], writeStreamRef);
            }
            else
            {
                // notifyClient((UInt8*)"0\r\n", writeStreamRef);
            }
        }
        else if (taskType == TASK_USLEEP)
        {
            if (writeStreamRef)
            {
                int usleepTime = 0;
                @try{
                    usleepTime = atoi((char*)eventData);
                }
                @catch (NSException *exception) {
                    iAntsControlAbilityLog(@"Debug: %@", exception.reason);
                    return;
                }
                // iAntsControlAbilityLog(@"sleep %d microseconds", usleepTime);
                usleep(usleepTime);
                notifyClient((UInt8*)"0;;Sleep ends\r\n", writeStreamRef); 
            }
            else
            {
                int usleepTime = 0;

                @try{
                    usleepTime = atoi((char*)eventData);
                }
                @catch (NSException *exception) {
                    iAntsControlAbilityLog(@"Debug: %@", exception.reason);
                    return;
                }
                // iAntsControlAbilityLog(@"sleep %d microseconds", usleepTime);
                usleep(usleepTime);
            }

        }
        else if (taskType == TASK_RUN_SHELL)
        {
            NSTask *task = [[NSTask alloc] init];

            // 设置执行的命令和参数
            NSString *launchPath = [NSString stringWithFormat:@"%@usr/bin/sudo", getJbPath()];
            iAntsControlAbilityLog(@"launchPath: %@", launchPath);
            [task setLaunchPath:launchPath];
            [task setArguments:@[[NSString stringWithFormat:@"sudo zxtouchb -e \"%s\"", eventData]]];

            // 设置输出管道，如果需要获取命令的输出
            NSPipe *pipe = [NSPipe pipe];
            [task setStandardOutput:pipe];

            // 启动任务
            [task launch];

            // 等待任务完成
            [task waitUntilExit];

            // 如果需要获取命令的输出，可以使用以下代码
            NSFileHandle *fileHandle = [pipe fileHandleForReading];
            NSData *data = [fileHandle readDataToEndOfFile];
            NSString *__unused output = [[NSString alloc] initWithData:data encoding:NSUTF8StringEncoding];
            iAntsControlAbilityLog(@"Command Output:\n%@", output);

            // system([[NSString stringWithFormat:@"sudo zxtouchb -e \"%s\"", eventData] UTF8String]);
            notifyClient((UInt8*)"0\r\n", writeStreamRef);
        }
        else if (taskType == TASK_TOUCH_RECORDING_START)
        {
            NSError *err = nil;
            startRecording(writeStreamRef, &err);    
            if (err)
            {
                notifyClient((UInt8*)[[err localizedDescription] UTF8String], writeStreamRef);
            }
            else
            {
                notifyClient((UInt8*)"0\r\n", writeStreamRef);
            }
        }
        else if (taskType == TASK_TOUCH_RECORDING_STOP)
        {
            stopRecording(); 
            notifyClient((UInt8*)"0\r\n", writeStreamRef); 
        }
        else if (taskType == TASK_SHOW_TOAST)
        {
            NSError *err = nil;
            showToastFromRawData(eventData, &err);
            if (err)
            {
                notifyClient((UInt8*)[[err localizedDescription] UTF8String], writeStreamRef);
            }
            else
            {
                notifyClient((UInt8*)"0\r\n", writeStreamRef);
            }
        }
        else if (taskType == TASK_TEXT_INPUT)
        {
            NSError *err = nil;
            NSString *result = inputTextFromRawData(eventData,  &err);
            if (err)
            {
                notifyClient((UInt8*)[[err localizedDescription] UTF8String], writeStreamRef);
            }
            else
            {
                notifyClient((UInt8*)[[NSString stringWithFormat:@"0;;%@\r\n", result] UTF8String], writeStreamRef);
            }
        }
        else if (taskType == TASK_TEXT_INPUT_BY_PASTE)
        {
            NSError *err = nil;
            NSString *result = inputTextPasteFromRawData(eventData,  &err);
            if (err)
            {
                notifyClient((UInt8*)[[err localizedDescription] UTF8String], writeStreamRef);
            }
            else
            {
                notifyClient((UInt8*)[[NSString stringWithFormat:@"0;;%@\r\n", result] UTF8String], writeStreamRef);
            }
        }
        else if (taskType == TASK_GET_DEVICE_INFO)
        {
            NSError *err = nil;
            NSString *deviceInfo = getDeviceInfoFromRawData(eventData,  &err);
            if (err)
            {
                notifyClient((UInt8*)[[err localizedDescription] UTF8String], writeStreamRef);
            }
            else
            {
                notifyClient((UInt8*)[[NSString stringWithFormat:@"0;;%@\r\n", deviceInfo] UTF8String], writeStreamRef);
            }
        }
        else if (taskType == TASK_TOUCH_INDICATOR)
        {
            // iAntsControlAbilityLog(@"控制触摸显示器");
            NSError *err = nil;
            handleTouchIndicatorTaskWithRawData(eventData, &err);
            if (err)
            {
                iAntsControlAbilityLog(@"失败: %@", [err localizedDescription]);
                notifyClient((UInt8*)[[err localizedDescription] UTF8String], writeStreamRef);
            }
            else
            {
                iAntsControlAbilityLog(@"成功");
                notifyClient((UInt8*)"0\r\n", writeStreamRef);
            }
        }
        else if (taskType == TASK_TEST)
        {

        }
        else if (taskType == TASK_SCREEN_SHOT) { // 截图，并返回json数据
            NSError *err = [NSError new];
            bool success = true;
            NSString *result = getScreenShotFromRawData(eventData, &err, &success);
            if (!success && err) {
                iAntsControlAbilityLog(@"OCR error %@", [err localizedDescription]);
                NSString *errorDesc = [err localizedDescription] ?: @"Unknown error";
                const char *msg = [errorDesc UTF8String];
                UInt8 *buffer = (UInt8*)strdup(msg);
                notifyClient(buffer, writeStreamRef);
                free(buffer);
                buffer = NULL;
            } else if (success && result) {
                iAntsControlAbilityLog(@"OCR success.");
                NSString *response = [NSString stringWithFormat:@"0;;%@\r\n", result];
                const char *respMsg = [response UTF8String];
                UInt8 *buffer = (UInt8*)strdup(respMsg);
                notifyClient(buffer, writeStreamRef);
                free(buffer);
                buffer = NULL;
            } else {
                iAntsControlAbilityLog(@"OCR empty.");
                const char *emptyMsg = "EMPTY_SCREENSHOT";
                notifyClient((UInt8*)emptyMsg, writeStreamRef);
            }
        }
        else if (taskType == TASK_KILL_APP)
        {
            pid_t pid = 0;
            const char* args[] = {"killall", "-9", (const char *)eventData, NULL};
            NSString *killallCommand = [NSString stringWithFormat:@"%@usr/bin/killall", getJbPath()];
            iAntsControlAbilityLog(@"killallCommand: %@", killallCommand);
            int status = posix_spawn(&pid, killallCommand.UTF8String, NULL, NULL, (char* const*)args, NULL);
            if (status != 0) {
                iAntsControlAbilityLog(@"posix_spawn failed: %s", strerror(status));
                // notifyClient((UInt8*)"kill app failed.\r\n", writeStreamRef);
            } else {
                iAntsControlAbilityLog(@"posix_spawn success");
                int status = -1;
                waitpid(pid, &status, 0);
                // notifyClient((UInt8*)"0\r\n", writeStreamRef);
            }
        }
        else if (taskType == TASK_SCREEN_SHOT_SHOWUI) { // 截图，并返回json数据
            NSError *err = [NSError new];
            bool success = true;
            NSString *result = getScreenShot4ShowUIFromRawData(eventData, &err, &success);
            if (!success && err) {
                iAntsControlAbilityLog(@"SHOWUI error %@", [err localizedDescription]);
                NSString *errorDesc = [err localizedDescription] ?: @"Unknown error";
                const char *msg = [errorDesc UTF8String];
                UInt8 *buffer = (UInt8*)strdup(msg);
                notifyClient(buffer, writeStreamRef);
                free(buffer);
                buffer = NULL;
            } else if (success && result) {
                iAntsControlAbilityLog(@"SHOWUI success.");
                NSString *response = [NSString stringWithFormat:@"0;;%@\r\n", result];
                const char *respMsg = [response UTF8String];
                UInt8 *buffer = (UInt8*)strdup(respMsg);
                notifyClient(buffer, writeStreamRef);
                free(buffer);
                buffer = NULL;
            } else {
                iAntsControlAbilityLog(@"SHOWUI empty.");
                const char *emptyMsg = "EMPTY_SCREENSHOT";
                notifyClient((UInt8*)emptyMsg, writeStreamRef);
            }
        }
        else if (taskType == TASK_PERFORM_TOUCH_PROPORTION)
        {
            performTouchProportionFromRawData(eventData);
        }
        else if (taskType == TASK_SCREEN_SHOT_SAVE) { // 截图，并返回json数据
            NSError *err = [NSError new];
            bool success = true;
            NSString *result = getScreenShot4SaveFromRawData(eventData, &err, &success);
            if (!success && err) {
                iAntsControlAbilityLog(@"SAVE error %@", [err localizedDescription]);
                NSString *errorDesc = [err localizedDescription] ?: @"Unknown error";
                const char *msg = [errorDesc UTF8String];
                UInt8 *buffer = (UInt8*)strdup(msg);
                notifyClient(buffer, writeStreamRef);
                free(buffer);
                buffer = NULL;
            } else if (success && result) {
                iAntsControlAbilityLog(@"SAVE success.");
                NSString *response = [NSString stringWithFormat:@"0;;%@\r\n", result];
                const char *respMsg = [response UTF8String];
                UInt8 *buffer = (UInt8*)strdup(respMsg);
                notifyClient(buffer, writeStreamRef);
                free(buffer);
                buffer = NULL;
            } else {
                iAntsControlAbilityLog(@"SAVE empty.");
                const char *emptyMsg = "EMPTY_SAVE";
                notifyClient((UInt8*)emptyMsg, writeStreamRef);
            }
        } 
        else if (taskType == TASK_SCREEN_SHOT_STATE) { // 截图，并返回json数据
            NSError *err = [NSError new];
            bool success = true;
            NSString *result = getScreenShot4StateFromRawData(eventData, &err, &success);
            if (!success && err) {
                iAntsControlAbilityLog(@"STATE error %@", [err localizedDescription]);
                NSString *errorDesc = [err localizedDescription] ?: @"Unknown error";
                const char *msg = [errorDesc UTF8String];
                UInt8 *buffer = (UInt8*)strdup(msg);
                notifyClient(buffer, writeStreamRef);
                free(buffer);
                buffer = NULL;
            } else if (success && result) {
                iAntsControlAbilityLog(@"STATE success. %@", result);
                NSString *response = [NSString stringWithFormat:@"0;;%@\r\n", result];
                const char *respMsg = [response UTF8String];
                UInt8 *buffer = (UInt8*)strdup(respMsg);
                notifyClient(buffer, writeStreamRef);
                free(buffer);
                buffer = NULL;
            } else {
                iAntsControlAbilityLog(@"STATE empty.");
                const char *emptyMsg = "EMPTY_STATE";
                notifyClient((UInt8*)emptyMsg, writeStreamRef);
            }
        }
        else if (taskType == TASK_CAPTCHA_HANDLE) { // 验证码处理
            NSError *err = [NSError new];
            bool success = true;
            NSString *result = getScreenShot4CaptchaHandleFromRawData(eventData, &err, &success);
            if (!success && err) {
                iAntsControlAbilityLog(@"CAPTCHA error %@", [err localizedDescription]);
                NSString *errorDesc = [err localizedDescription] ?: @"Unknown error";
                const char *msg = [errorDesc UTF8String];
                UInt8 *buffer = (UInt8*)strdup(msg);
                notifyClient(buffer, writeStreamRef);
                free(buffer);
                buffer = NULL;
            } else if (success && result) {
                iAntsControlAbilityLog(@"CAPTCHA success.");
                NSString *response = [NSString stringWithFormat:@"0;;%@\r\n", result];
                const char *respMsg = [response UTF8String];
                UInt8 *buffer = (UInt8*)strdup(respMsg);
                notifyClient(buffer, writeStreamRef);
                free(buffer);
                buffer = NULL;
            } else {
                iAntsControlAbilityLog(@"CAPTCHA empty.");
                const char *emptyMsg = "EMPTY_STATE";
                notifyClient((UInt8*)emptyMsg, writeStreamRef);
            }
        } 
    }

}