#import "../Common.h"

// https://zhangkn.github.io/2018/03/SBUserNotificationAlert/

int checkAlertHeaderMatch(NSString *alertHeader) {
    NSDictionary *config = getSysytemAlertHandleConfig();
    iAntsControlAbilityLog(@"[SBUserNotificationAlert] config %@", config);
    if (!config || config.count == 0) return -1;
    
    for (NSString *key in config) {
        id matchesValue = config[key];
        if (!(matchesValue && [matchesValue isKindOfClass:[NSArray class]])) continue;
        NSArray *matches = (NSArray *)matchesValue;
        for (id matchValue in matches) {
            if (![matchValue isKindOfClass:[NSDictionary class]]) continue;
            NSDictionary *match = (NSDictionary *)matchValue;
            id typeValue = match[@"type"];
            id valueValue = match[@"value"];
            if (typeValue && valueValue && 
                [typeValue isKindOfClass:[NSString class]] && [valueValue isKindOfClass:[NSArray class]]) {
                NSString *type = (NSString *)typeValue;
                NSArray *value = (NSArray *)valueValue;
                if ([type isEqualToString:@"include"]) { // 字符串包含
                    for (NSString *v in value) {
                        if ([alertHeader containsString:v]) {
                            return [key intValue];
                        }
                    }
                } else if ([type isEqualToString:@"regexp"]) { // 正则匹配
                    for (NSString *v in value) {
                        NSRegularExpression *regex = [[NSRegularExpression alloc] initWithPattern:v options:0 error:nil];
                        if (!regex) continue;
                        NSUInteger result = [regex numberOfMatchesInString:alertHeader options:0 range:NSMakeRange(0, alertHeader.length)];
                        if(result != 0) {
                            return [key intValue];
                        }

                    }
                }
            } 
        }
    }
    return -1;
}

@interface SBUserNotificationAlert
@property (retain) NSString * alertHeader;
@property (retain) NSString * alertMessage;
@property (retain) NSString * alternateButtonTitle;
@property (retain) NSString * otherButtonTitle;
@property (retain) NSString * defaultButtonTitle;

-(void)dismissIfNecessaryWithResponse:(int)arg1;
@end
%hook SBUserNotificationAlert

- (void)willActivate {
    iAntsControlAbilityLog(@"alertHeader %@", [self alertHeader]);
    iAntsControlAbilityLog(@"alertMessage %@", [self alertMessage]);
    iAntsControlAbilityLog(@"alternateButtonTitle %@", [self alternateButtonTitle]);
    iAntsControlAbilityLog(@"otherButtonTitle %@", [self otherButtonTitle]);
    iAntsControlAbilityLog(@"defaultButtonTitle %@", [self defaultButtonTitle]);

    NSString *alertHeader = [self alertHeader];
    int response = -1;
    if (alertHeader) {
        response = checkAlertHeaderMatch(alertHeader);
        NSLog(@"[iAntsControlAbility] alertHeader: %@ response %d", alertHeader, response);
    } else {
        iAntsControlAbilityLog(@"alertHeader is nil");
    }
    %orig;
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        if (response != -1) {
            iAntsControlAbilityLog(@"dismissIfNecessaryWithResponse %d", response);
            [self dismissIfNecessaryWithResponse:response];
        }
    });
}
%end