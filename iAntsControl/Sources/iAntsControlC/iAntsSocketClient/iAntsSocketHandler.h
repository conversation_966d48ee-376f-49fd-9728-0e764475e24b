//
//  iAntsSocketHandler.h
//  iAntsTouchAbility
//
//  Created by iAnts on 2024/06/06.
//

#import <Foundation/Foundation.h>
#import "iAntsSocketClient.h"
#import "LogC.h"

NS_ASSUME_NONNULL_BEGIN

/// iAnts Socket消息处理器 - 负责处理WebSocket消息并调用相应的功能模块
@interface iAntsSocketHandler : NSObject <IAntsSocketClientDelegate>

/// 单例访问
+ (instancetype)shared;

// 移除默认配置方法 - 必须使用initializeAndConnectWithConfig:方法并提供完整配置

/// 初始化并启动WebSocket客户端（使用自定义配置）
- (BOOL)initializeAndConnectWithConfig:(IAntsSocketConfig *)config;

/// 断开连接
- (void)disconnect;

/// 获取连接状态
@property (nonatomic, readonly) BOOL isConnected;

/// 获取WebSocket客户端实例（只读）
@property (nonatomic, strong, readonly) IAntsSocketClient *socketClient;

@end

NS_ASSUME_NONNULL_END 