//
//  iAntsSocketClient.h
//  iAnts WebSocket Client
//
//  Created by iAnts on 2024/01/01.
//  Copyright © 2024 iAnts. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "iAntsSocketPB.h"
#import "LogC.h"

@class IAntsMessage;
@class IAntsSocketConfig;

NS_ASSUME_NONNULL_BEGIN

/// WebSocket客户端配置类
@interface IAntsSocketConfig : NSObject

/// WebSocket服务器URL
@property (nonatomic, strong, readonly) NSString *serverURL;

/// 最大重连尝试次数
@property (nonatomic, assign, readonly) NSInteger maxReconnectAttempts;

/// 是否启用自动重连
@property (nonatomic, assign, readonly) BOOL autoReconnectEnabled;

/// 是否启用心跳机制
@property (nonatomic, assign, readonly) BOOL heartbeatEnabled;

/// 心跳间隔时间（秒）
@property (nonatomic, assign, readonly) NSTimeInterval heartbeatInterval;

// 移除默认配置，要求调用者必须提供所有参数

/// 自定义配置初始化
- (instancetype)initWithServerURL:(NSString *)serverURL
              maxReconnectAttempts:(NSInteger)maxReconnectAttempts
              autoReconnectEnabled:(BOOL)autoReconnectEnabled
                   heartbeatEnabled:(BOOL)heartbeatEnabled
                  heartbeatInterval:(NSTimeInterval)heartbeatInterval;

@end

/// WebSocket客户端委托协议
@protocol IAntsSocketClientDelegate <NSObject>

@required
/// WebSocket连接成功回调
- (void)webSocketDidConnect;

/// WebSocket连接断开回调
/// @param error 错误信息，正常断开时为nil
- (void)webSocketDidDisconnect:(NSError * _Nullable)error;

/// 收到WebSocket消息回调
/// @param messageType 消息类型对象
/// @param messageContent 消息内容字典
- (void)webSocketDidReceiveMessage:(IAntsMessageType *)messageType content:(NSDictionary *)messageContent;

@optional
/// 发送消息前回调
/// @param messageType 消息类型对象
/// @param messageContent 消息内容字典
- (void)webSocketWillSendMessage:(IAntsMessageType *)messageType content:(NSDictionary *)messageContent;

/// 发送消息结果回调
/// @param messageType 消息类型对象
/// @param success 是否发送成功
/// @param error 错误信息，成功时为nil
- (void)webSocketDidSendMessage:(IAntsMessageType *)messageType success:(BOOL)success error:(NSError * _Nullable)error;

@end

/// 消息处理器块类型定义
typedef void (^IAntsMessageHandler)(IAntsMessageType *messageType, NSDictionary *messageContent);

/// iAnts WebSocket客户端 - 使用新的iAntsSocketPB架构
@interface IAntsSocketClient : NSObject

/// 委托对象（弱引用）
@property (nonatomic, weak, nullable) id<IAntsSocketClientDelegate> delegate;

/// 连接状态（只读）
@property (nonatomic, readonly, getter=isConnected) BOOL connected;

/// 当前配置（只读）
@property (nonatomic, strong, readonly) IAntsSocketConfig *config;

/// 单例对象
+ (instancetype)shared;

/// 使用配置初始化并连接
- (BOOL)initializeAndConnectWithConfig:(IAntsSocketConfig *)config;

/// 连接到WebSocket服务器
- (BOOL)connect;

/// 断开WebSocket连接
- (void)disconnect;

/// 发送消息到服务器（使用IAntsMessage对象）
/// @param message 消息对象
/// @return 是否成功发送
- (BOOL)sendMessage:(IAntsMessage *)message;

/// 发送消息到服务器（使用消息类型和内容）
/// @param messageType 消息类型对象
/// @param content 消息内容字典
/// @return 是否成功发送
- (BOOL)sendMessageType:(IAntsMessageType *)messageType content:(NSDictionary *)content;

/// 发送心跳消息
/// @param version 版本信息
/// @return 是否成功发送
- (BOOL)sendHeartbeatWithVersion:(NSString *)version;

/// 注册消息处理器
/// @param messageType 消息类型对象
/// @param handler 处理器块
- (void)registerHandlerForMessageType:(IAntsMessageType *)messageType handler:(IAntsMessageHandler)handler;

/// 移除消息处理器
/// @param messageType 消息类型对象
- (void)removeHandlerForMessageType:(IAntsMessageType *)messageType;

/// 启用/禁用自动重连
/// @param enabled 是否启用
- (void)setAutoReconnectEnabled:(BOOL)enabled;

/// 启用/禁用心跳定时器
/// @param enabled 是否启用
- (void)setHeartbeatEnabled:(BOOL)enabled;

/// 重置客户端状态
- (void)reset;

@end

NS_ASSUME_NONNULL_END