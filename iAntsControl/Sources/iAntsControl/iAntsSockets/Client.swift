import Foundation
import SwiftProtobuf

/// RemoteClient - WebSocket 客户端单例
/// 负责连接 ws://127.0.0.1:6888/rtcpipe，处理消息收发和断线重连
class RemoteClient {

    // MARK: - 单例
    static let shared = RemoteClient()

    // MARK: - 属性
    private let logger = DaLog(subsystem: "com.iantsrtc.socket", category: "RemoteClient")
    private let wsURL = URL(string: "ws://127.0.0.1:6888/rtcpipe")!

    // WebSocket 客户端
    private var wsClient: WSClient?

    // 重连相关
    private var reconnectTask: Task<Void, Never>?
    private var heartbeatTask: Task<Void, Never>?
    private var isStarted = false

    // 重连配置
    private let maxRetryInterval: TimeInterval = 300 // 5分钟
    private let baseRetryInterval: TimeInterval = 0.5  // 0.5秒，第一次重试更快
    private var currentRetryInterval: TimeInterval = 0.5

    // MARK: - 初始化
    private init() {
        logger.info("🔌 RemoteClient 初始化完成，未连接")
    }

    deinit {
        logger.info("♻️ RemoteClient 析构")
        reconnectTask?.cancel()
        heartbeatTask?.cancel()
    }

    // MARK: - 公共接口

    /// 启动 WebSocket 连接和消息监听
    func start() async {
        guard !isStarted else {
            logger.info("⚠️ RemoteClient 已经启动，跳过重复启动")
            return
        }

        isStarted = true
        logger.info("🚀 启动 RemoteClient")

        // 启动连接任务
        startConnectionTask()
    }

    /// 停止 WebSocket 连接
    func stop() async {
        guard isStarted else { return }

        isStarted = false
        logger.info("⏹️ 停止 RemoteClient")

        // 取消所有任务
        reconnectTask?.cancel()
        heartbeatTask?.cancel()

        // 断开连接
        if let client = wsClient {
            client.disconnect()
            wsClient = nil
        }

        // 重置重连间隔
        currentRetryInterval = baseRetryInterval
    }

    // MARK: - 私有方法

    /// 启动连接任务
    private func startConnectionTask() {
        reconnectTask?.cancel()
        reconnectTask = Task { [weak self] in
            await self?.connectionLoop()
        }
    }

    /// 连接循环 - 负责连接和重连逻辑
    private func connectionLoop() async {
        while isStarted && !Task.isCancelled {
            do {
                // 创建新的 WebSocket 客户端
                let client = WSClient(serverURL: wsURL)
                client.delegate = self
                wsClient = client

                logger.info("🔄 尝试连接到 \(wsURL)")

                // 尝试连接
                try await client.connect()

                // 连接成功，等待断开
                while isStarted && !Task.isCancelled && client.state == .connected {
                    try? await Task.sleep(nanoseconds: 1_000_000_000) // 1秒检查一次
                }

            } catch {
                logger.error("❌ 连接失败: \(error)")
            }

            // 如果还需要继续运行，则等待后重连
            if isStarted && !Task.isCancelled {
                logger.info("⏳ \(currentRetryInterval)秒后重试连接")
                try? await Task.sleep(nanoseconds: UInt64(currentRetryInterval * 1_000_000_000))

                // 指数退避，最大5分钟
                currentRetryInterval = min(currentRetryInterval * 2, maxRetryInterval)
            }
        }
    }



    /// 启动心跳任务（全局唯一）
    private func startHeartbeat(client: WSClient) {
        // 如果已有心跳任务，先停止
        stopHeartbeat()

        // 使用独立的后台队列来执行心跳任务，避免被其他任务阻塞
        heartbeatTask = Task.detached { [weak self] in
            self?.logger.info("💓 心跳任务开始运行")

            while !Task.isCancelled {
                do {
                    // 发送心跳消息
                    await self?.sendHeartbeat(client: client)

                    // 等待3分钟
                    self?.logger.debug("💓 心跳任务等待3分钟...")
                    try await Task.sleep(nanoseconds: 180_000_000_000)
                    self?.logger.debug("💓 心跳任务等待结束，准备发送下一次心跳")
                } catch {
                    // 任务被取消或其他错误
                    self?.logger.info("💔 心跳任务结束: \(error)")
                    break
                }
            }

            self?.logger.info("💔 心跳任务循环结束")
        }

        logger.info("💓 启动心跳任务")
    }

    /// 停止心跳任务
    private func stopHeartbeat() {
        heartbeatTask?.cancel()
        heartbeatTask = nil
        logger.info("💔 停止心跳任务")
    }

    /// 发送心跳消息
    private func sendHeartbeat(client: WSClient) async {
        do {
            // 创建心跳消息
            var heartbeat = IAnts_Heartbeat()
            heartbeat.version = DAEMON.version

            var message = IAnts_RTCMessage()
            // 正确设置消息类型和消息体
            message.messageType = .commonType(.heartbeat)
            message.body = .heartbeat(heartbeat)

            let data = try message.serializedData()
            try await client.sendProtobufData(data)

            logger.debug("💓 发送心跳")
        } catch {
            logger.error("❌ 发送心跳失败: \(error)")
        }
    }

    // MARK: - 消息处理

    /// 处理接收到的消息
    private func handleMessage(_ data: Data) async {
        do {
            // 反序列化 protobuf 消息
            let message = try IAnts_RTCMessage(serializedBytes: data)
            logger.debug("📨 收到消息，数据长度: \(data.count) bytes")

            // 根据消息类型处理
            if let messageType = message.messageType {
                switch messageType {
                case .commonType(_):
                    await handleCommonMessage(message)
                case .rtcType(_):
                    await handleRTCMessage(message)
                }
            } else {
                logger.warning("⚠️ 收到未知消息类型，完整消息: \(message)")
            }

        } catch {
            logger.error("❌ 消息反序列化失败: \(error), 数据长度: \(data.count) bytes, 数据内容: \(data.prefix(100))")
        }
    }

    /// 处理公共消息
    private func handleCommonMessage(_ message: IAnts_RTCMessage) async {
        guard let messageType = message.messageType, case .commonType(let commonType) = messageType else {
            logger.warning("⚠️ 公共消息类型解析失败")
            return
        }

        switch commonType {
        case .heartbeat:
            logger.debug("💓 收到心跳响应: \(message): \(String(describing: message.body))")
            // TODO: 处理心跳响应 暂时没啥作用


        case .error:
            if let body = message.body, case .error(let errorMsg) = body {
                logger.error("❌ 收到错误消息: \(errorMsg.text), 完整消息: \(message)")
            } else {
                logger.error("❌ 收到错误消息但解析失败: \(message)")
            }

        case .UNRECOGNIZED(_):
            logger.warning("⚠️ 收到未识别的公共消息类型: \(message)：\(commonType) ")
        }
    }

    /// 处理 RTC 消息
    private func handleRTCMessage(_ message: IAnts_RTCMessage) async {
        guard let messageType = message.messageType, case .rtcType(let rtcType) = messageType else {
            logger.warning("⚠️ RTC 消息类型解析失败")
            return
        }

        switch rtcType {
        case .rtcUnknown:
            logger.warning("⚠️ 收到 RTC_UNKNOWN 消息，跳过处理: \(message)")

        case .rtcStatus:
            logger.info("📊 收到状态请求: \(message)")
            await handleStatusRequest(message) 

        case .rtcDataantAction:
            logger.info("🎯 收到 DataAnt 动作请求: \(message)")
            await handleDataAntAction(message)

        case .rtcIantsAction:
            logger.info("🎯 收到 iAnts 动作请求: \(message)")
            await handleIAntsAction(message)

        case .rtcScreenshot:
            logger.info("📸 收到截图请求: \(message)")
            await handleScreenshotRequest(message)

        case .rtcScreenCapture:
            logger.info("🎬 收到录屏请求: \(message)")
            await handleScreenCaptureRequest(message)

        case .rtcRecordAudio:
            logger.info("🎤 收到录音请求: \(message)")
            await handleRecordAudioRequest(message)

        case .UNRECOGNIZED(_):
            logger.warning("⚠️ 收到未识别的 RTC 消息类型: \(message)")
        }
    }

    // MARK: - 具体消息处理方法

    /// 处理状态请求
    private func handleStatusRequest(_ message: IAnts_RTCMessage) async {
        guard let body = message.body, case .rtcStatus(let statusMessage) = body else {
            logger.error("❌ 状态请求消息格式错误")
            return
        }

        logger.info("📊 处理状态请求，requestId: \(statusMessage.requestID)")

        // 获取 RtcSessionManager 状态
        let managerStatus = await RtcSessionManager.shared.getManagerStatus()
        logger.info("📊 当前状态: \(managerStatus)")

        // 构造状态响应消息
        var responseStatus = IAnts_RTC_StatusMessage()
        responseStatus.requestID = statusMessage.requestID
        responseStatus.success = true
        responseStatus.timestamp = Int64(Date().timeIntervalSince1970)

        // 填充状态信息
        responseStatus.sessionCount = Int64(managerStatus["sessionCount"] as? Int ?? 0)

        // 解析信令状态
        if let signalingClient = managerStatus["signalingClient"] as? [String: Any],
           let connected = signalingClient["connected"] as? Bool {
            responseStatus.signalingStatus = connected ? "connected" : "disconnected"
        } else {
            responseStatus.signalingStatus = "unknown"
        }

        // 解析屏幕状态
        if let screenCapture = managerStatus["screenCapture"] as? [String: Any],
           let delegateCount = screenCapture["delegateCount"] as? Int {
            responseStatus.screenStatus = delegateCount > 0 ? "active" : "inactive"
        } else {
            responseStatus.screenStatus = "unknown"
        }

        responseStatus.connectRetryCount = Int32(managerStatus["connectRetryCount"] as? Int ?? 0)

        // 构造完整响应消息
        var responseMessage = IAnts_RTCMessage()
        responseMessage.messageType = .rtcType(.rtcStatus)
        responseMessage.body = .rtcStatus(responseStatus)

        // 发送响应
        await sendResponse(responseMessage)
        logger.info("📊 状态响应已发送，requestId: \(statusMessage.requestID)")
    }

    /// 处理 DataAnt 动作请求
    private func handleDataAntAction(_ message: IAnts_RTCMessage) async {
        guard let body = message.body, case .rtcDataantAction(_) = body else {
            logger.error("❌ DataAnt 动作消息格式错误")
            return
        }

        logger.info("🎯 启动 RtcSessionManager (DataAnt)")
        let result = await RtcSessionManager.shared.start()

        if result.success {
            logger.info("✅ RtcSessionManager 启动成功")
        } else {
            logger.error("❌ RtcSessionManager 启动失败: \(result.error ?? "未知错误")")
        }
    }

    /// 处理 iAnts 动作请求
    private func handleIAntsAction(_ message: IAnts_RTCMessage) async {
        guard let body = message.body, case .rtcIantsAction(_) = body else {
            logger.error("❌ iAnts 动作消息格式错误")
            return
        }

        logger.info("🎯 启动 RtcSessionManager (iAnts)")
        let result = await RtcSessionManager.shared.start()

        if result.success {
            logger.info("✅ RtcSessionManager 启动成功")
        } else {
            logger.error("❌ RtcSessionManager 启动失败: \(result.error ?? "未知错误")")
        }
    }

    /// 处理截图请求
    private func handleScreenshotRequest(_ message: IAnts_RTCMessage) async {
        guard let body = message.body, case .rtcScreenshot(let screenshotMessage) = body else {
            logger.error("❌ 截图请求消息格式错误")
            return
        }

        logger.info("📸 执行截图操作，requestId: \(screenshotMessage.requestID)")

        // 构造截图参数
        let screenshotParams = ScreenshotParams(
            requestId: screenshotMessage.requestID,
            path: screenshotMessage.path.isEmpty ? nil : screenshotMessage.path,
            isBase64: screenshotMessage.isBase64,
            isCompress: screenshotMessage.isCompress,
            quality: screenshotMessage.quality > 0 ? screenshotMessage.quality : 100,
            format: screenshotMessage.format.isEmpty ? "png" : screenshotMessage.format,
            regionX: screenshotMessage.regionX,
            regionY: screenshotMessage.regionY,
            regionWidth: screenshotMessage.regionWidth,
            regionHeight: screenshotMessage.regionHeight
        )

        // 执行截图操作
        let screenshotResult = await ScreenShot.shared.takeScreenshotWithParams(screenshotParams)

        // 构造响应消息
        var responseScreenshot = IAnts_RTC_ScreenshotMessage()
        responseScreenshot.requestID = screenshotMessage.requestID
        responseScreenshot.success = screenshotResult.success
        responseScreenshot.path = screenshotResult.path ?? ""
        responseScreenshot.imageWidth = Int32(screenshotResult.width)
        responseScreenshot.imageHeight = Int32(screenshotResult.height)
        responseScreenshot.fileSize = screenshotResult.fileSize

        // 如果请求了 base64 数据且截图成功
        if screenshotMessage.isBase64 && screenshotResult.success {
            responseScreenshot.data = screenshotResult.base64Data ?? ""
        }

        if !screenshotResult.success {
            responseScreenshot.errorMessage = screenshotResult.errorMessage ?? "截图失败"
        }

        logger.info("📸 截图完成: \(screenshotResult.success ? "成功" : "失败")")

        // 构造完整响应消息
        var responseMessage = IAnts_RTCMessage()
        responseMessage.messageType = .rtcType(.rtcScreenshot)
        responseMessage.body = .rtcScreenshot(responseScreenshot)

        // 发送响应
        await sendResponse(responseMessage)
        logger.info("📸 截图响应已发送，requestId: \(screenshotMessage.requestID)")
    }

    /// 处理录屏请求
    private func handleScreenCaptureRequest(_ message: IAnts_RTCMessage) async {
        guard let body = message.body, case .rtcScreenCapture(let captureMessage) = body else {
            logger.error("❌ 录屏请求消息格式错误")
            return
        }

        logger.info("🎬 处理录屏请求，动作: \(captureMessage.action)")

        switch captureMessage.action {
        case .screenCaptureStart:
            await ScreenRecorder.shared.startRecording()

        case .screenCaptureStop:
            await ScreenRecorder.shared.stopRecording()

        case .screenCaptureStartTimed:
            // 定时录屏，可以根据 duration 参数实现
            await ScreenRecorder.shared.startTimedRecording(duration: Int(captureMessage.duration))

        default:
            logger.warning("⚠️ 未支持的录屏动作: \(captureMessage.action)")
        }
    }

    /// 处理录音请求
    private func handleRecordAudioRequest(_ message: IAnts_RTCMessage) async {
        guard let body = message.body, case .rtcRecordAudio(_) = body else {
            logger.error("❌ 录音请求消息格式错误")
            return
        }

        logger.info("🎤 收到录音请求，暂未实现，跳过处理")
        // TODO: 实现录音功能
    }

    // MARK: - 响应消息发送

    /// 发送响应消息
    private func sendResponse(_ responseMessage: IAnts_RTCMessage) async {
        guard let client = wsClient else {
            logger.error("❌ WebSocket 客户端未连接，无法发送响应")
            return
        }

        do {
            let data = try responseMessage.serializedData()
            try await client.sendProtobufData(data)
            logger.debug("📤 发送响应消息成功")
        } catch {
            logger.error("❌ 发送响应消息失败: \(error)")
        }
    }
}

// MARK: - WSClientDelegate
extension RemoteClient: WSClientDelegate {
    func wsClientDidConnect(_ client: WSClient) {
        // 连接成功，重置重连间隔
        currentRetryInterval = baseRetryInterval
        logger.info("✅ WebSocket 连接成功")

        // 启动心跳
        startHeartbeat(client: client)
    }

    func wsClientDidDisconnect(_ client: WSClient, error: Error?) {
        // 连接断开或出错，停止心跳
        stopHeartbeat()
        if let error = error {
            logger.error("❌ WebSocket 连接断开: \(error)")
        } else {
            logger.info("❌ WebSocket 连接断开")
        }

        // 如果不是手动断开，触发重连
        if isStarted {
            logger.info("🔄 连接断开，将触发重连...")
        }
    }

    func wsClient(_ client: WSClient, didReceiveData data: Data) {
        // 使用独立的后台队列处理消息，避免阻塞其他任务
        Task.detached { [weak self] in
            await self?.handleMessage(data)
        }
    }

    func wsClient(_ client: WSClient, didReceiveError error: Error) {
        logger.error("❌ WebSocket 错误: \(error)")
    }
}
