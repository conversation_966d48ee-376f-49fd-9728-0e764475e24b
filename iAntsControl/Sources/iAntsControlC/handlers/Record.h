#ifndef RECORD_H
#define RECORD_H

#ifdef __cplusplus
extern "C" {
#endif

#include "IOHIDEvent.h"
#include "IOHIDEventData.h"
#include "IOHIDEventTypes.h"
#include "IOHIDEventSystemClient.h"
#include "IOHIDEventSystem.h"

#include <mach/mach_time.h>

#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>
#include <stdint.h>
#import <CoreFoundation/CoreFoundation.h>

void startRecording(CFWriteStreamRef requestClient, NSError **error);
void stopRecording();
static void recordIOHIDEventCallback(void* target, void* refcon, IOHIDServiceRef service, IOHIDEventRef event);
Boolean isRecordingStart();

#ifdef __cplusplus
}
#endif

#endif