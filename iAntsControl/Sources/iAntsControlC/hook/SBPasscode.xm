#import <substrate.h>
#import <notify.h>
#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>
#import <dlfcn.h>
#import <mach/mach_time.h>
#import "../Common.h"

@interface NCNotificationCombinedListViewController : UIViewController
- (BOOL)hasContent;
@end

@interface SBLockScreenManager : NSObject
+(id)sharedInstance;
- (BOOL)isUILocked;
- (BOOL)_attemptUnlockWithPasscode:(id)arg1 mesa:(BOOL)arg2 finishUIUnlock:(BOOL)arg3 completion:(id)arg4;
- (BOOL)_attemptUnlockWithPasscode:(id)arg1 mesa:(BOOL)arg2 finishUIUnlock:(BOOL)arg3;
- (BOOL)_attemptUnlockWithPasscode:(id)arg1 finishUIUnlock:(BOOL)arg2;
- (void)attemptUnlockWithPasscode:(id)arg1 completion:(id)arg2;
- (BOOL)attemptUnlockWithPasscode:(id)arg1;
- (void)lockUIFromSource:(int)arg1 withOptions:(id)arg2;
- (id)lockScreenViewController;
- (void)unlockUIFromSource:(int)source withOptions:(NSDictionary *)options;
@end

@interface SBUserAgent : NSObject
+ (id)sharedUserAgent;
- (void)undimScreen;
@end

@interface SBBacklightController : NSObject
+ (id)sharedInstance;
- (void)turnOnScreenFullyWithBacklightSource:(int)arg1;
@end

static BOOL screenIsLocked;
static BOOL isBlackScreen;
static BOOL hasVisibleBulletins;
static NSString* originalPasscode;

// 获取是否启用了自动解锁功能
static BOOL shouldAutoUnlock() {
    // 检查UserDefaults中的iants_prevent_autolock设置
    BOOL preventAutoLock = [[NSUserDefaults standardUserDefaults] boolForKey:@"iants_prevent_autolock"];
    iAntsControlAbilityLog(@"[SBPasscode] 检查自动解锁设置: %@", preventAutoLock ? @"开启" : @"关闭");
    return preventAutoLock;
}

static void unlockDeviceNow(NSString* plainTextPassword)
{
    // 检查是否应该自动解锁
    if (!shouldAutoUnlock()) {
        iAntsControlAbilityLog(@"[SBPasscode] 自动解锁功能已关闭，不执行解锁");
        return;
    }
    
    iAntsControlAbilityLog(@"[SBPasscode] 执行自动解锁，pwd:%@", plainTextPassword);
    if(!plainTextPassword) {
        return;
    }
    dispatch_async(dispatch_get_main_queue(), ^{
        SBLockScreenManager* SBLockSH = [%c(SBLockScreenManager) sharedInstance];
        if([SBLockSH respondsToSelector:@selector(_attemptUnlockWithPasscode:mesa:finishUIUnlock:completion:)]) {
            [SBLockSH _attemptUnlockWithPasscode:plainTextPassword mesa:0 finishUIUnlock:1 completion:NULL];
        } else if([SBLockSH respondsToSelector:@selector(_attemptUnlockWithPasscode:mesa:finishUIUnlock:)]) {
            [SBLockSH _attemptUnlockWithPasscode:plainTextPassword mesa:0 finishUIUnlock:1];
        } else if([SBLockSH respondsToSelector:@selector(_attemptUnlockWithPasscode:finishUIUnlock:)]) {
            [SBLockSH _attemptUnlockWithPasscode:plainTextPassword finishUIUnlock:1];
        } else if([SBLockSH respondsToSelector:@selector(attemptUnlockWithPasscode:)]) {
            [SBLockSH attemptUnlockWithPasscode:plainTextPassword];
        } else if([SBLockSH respondsToSelector:@selector(attemptUnlockWithPasscode:completion:)]) {
            [SBLockSH attemptUnlockWithPasscode:plainTextPassword completion:NULL];
        }
    });
}

static void passcodeReceived(NSString* plainTextPassword)
{
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, 1 * NSEC_PER_SEC), dispatch_get_main_queue(), ^{
        if(!screenIsLocked) {
            originalPasscode = [plainTextPassword copy];
        }
    });
}


static void turnOnScreen()
{
    // 检查是否应该自动解锁
    if (!shouldAutoUnlock()) {
        iAntsControlAbilityLog(@"[SBPasscode] 自动解锁功能已关闭，不执行点亮屏幕");
        return;
    }
    
    iAntsControlAbilityLog(@"[SBPasscode] 执行点亮屏幕");
    
    if (%c(SBLockScreenManager)) {
        SBLockScreenManager *manager = (SBLockScreenManager *)[%c(SBLockScreenManager) sharedInstance];
        NSDictionary *options = @{ @"SBUIUnlockOptionsTurnOnScreenFirstKey" : [NSNumber numberWithBool:YES] };
        [manager unlockUIFromSource:6 withOptions:options];

    }
    else if (%c(SBBacklightController)) {
        SBBacklightController *manager = (SBBacklightController *)[%c(SBBacklightController) sharedInstance];
        [manager turnOnScreenFullyWithBacklightSource:0];

    }
    else {
        //使用SBUserAgent 点亮屏幕会出现安全问题 崩溃
        SBUserAgent *agent = (SBUserAgent *)[%c(SBUserAgent) sharedUserAgent];
        [agent undimScreen];
    }


    unlockDeviceNow(@"000000");
}

%hook SBLockScreenManager
-(void)attemptUnlockWithPasscode:(id)arg1 completion:(/*^block*/id)arg2
{
    %orig;
    if (!originalPasscode) {
        iAntsControlAbilityLog(@"-(BOOL)attemptUnlockWithPasscode:%@ completion:%@", arg1, arg2);
        passcodeReceived([arg1 copy]);
    }
}
- (BOOL)attemptUnlockWithPasscode:(id)arg1
{
    BOOL r = %orig;
    if (!originalPasscode) {
        iAntsControlAbilityLog(@"-(BOOL)attemptUnlockWithPasscode:%@", arg1);
        passcodeReceived([arg1 copy]);
    }
    return r;
}
- (BOOL)_attemptUnlockWithPasscode:(id)arg1 mesa:(BOOL)arg2 finishUIUnlock:(BOOL)arg3 completion:(id)arg4
{
    BOOL r = %orig;
    if (!originalPasscode) {
        iAntsControlAbilityLog(@"-(BOOL)_attemptUnlockWithPasscode:%@ mesa:%@ finishUIUnlock:%@ completion:%@", arg1, @(arg2), @(arg3), arg4);
        passcodeReceived([arg1 copy]);
    }
    return r;
}



- (void)lockUIFromSource:(int)arg1 withOptions:(id)arg2{
    // 检查是否应该自动解锁，才执行点亮屏幕和解锁操作
    if (shouldAutoUnlock()) {
        iAntsControlAbilityLog(@"[SBPasscode] 锁屏事件 - 将自动解锁");
        dispatch_async(dispatch_get_main_queue(), ^{
             turnOnScreen();
        });
    } else {
        iAntsControlAbilityLog(@"[SBPasscode] 锁屏事件 - 不自动解锁");
    }

    return %orig;
}

%end

%hook SBUIPasscodeLockViewWithKeypad
- (id)statusTitleView
{
    if (!originalPasscode) {
        UILabel *label = MSHookIvar<UILabel *>(self, "_statusTitleView");
        
        // 根据自动解锁状态显示不同的标签文本
        if (shouldAutoUnlock()) {
            label.text = @"[自动解锁已开启]";
        } else {
            label.text = @"[自动解锁已关闭]";
        }
        
        return label;
    }
    return %orig;
}
%end

%hook NCNotificationCombinedListViewController
- (void)viewWillLayoutSubviews
{
    %orig;
    hasVisibleBulletins = [self hasContent];
    iAntsControlAbilityLog(@"viewWillLayoutSubviews[hasVisibleBulletins: %@]:%@ ", @(hasVisibleBulletins), self);
}
%end

static void screenDisplayStatus(CFNotificationCenterRef center, void* observer, CFStringRef name, const void* object, CFDictionaryRef userInfo) {
    uint64_t state;
    int token;
    notify_register_check("com.apple.iokit.hid.displayStatus", &token);
    notify_get_state(token, &state);
    notify_cancel(token);
    if(!state) {
        iAntsControlAbilityLog(@"display was off");
        isBlackScreen = YES;
    } else {
        isBlackScreen = NO;
        // 检查是否应该自动解锁
        if(screenIsLocked && !hasVisibleBulletins && shouldAutoUnlock()) {
            unlockDeviceNow(originalPasscode);
        }
    }
}

static void screenLockStatus(CFNotificationCenterRef center, void* observer, CFStringRef name, const void* object, CFDictionaryRef userInfo)
{
    uint64_t state;
    int token;
    notify_register_check("com.apple.springboard.lockstate", &token);
    notify_get_state(token, &state);
    notify_cancel(token);
    if (state) {
        screenIsLocked = YES;
    } else {
        iAntsControlAbilityLog(@"device was unlocked");
        screenIsLocked = NO;
    }
}

// 永不锁屏设置变更通知回调
static void preventAutoLockChanged(CFNotificationCenterRef center, void* observer, CFStringRef name, const void* object, CFDictionaryRef userInfo)
{
    BOOL newSetting = [[NSUserDefaults standardUserDefaults] boolForKey:@"iants_prevent_autolock"];
    iAntsControlAbilityLog(@"[SBPasscode] 收到永不锁屏设置变更通知: %@", newSetting ? @"开启" : @"关闭");
    
    // 如果设置开启了，并且当前屏幕是锁定状态，则立即执行解锁
    if (newSetting && screenIsLocked) {
        iAntsControlAbilityLog(@"[SBPasscode] 设置已更改为开启，立即执行解锁");
        dispatch_async(dispatch_get_main_queue(), ^{
            turnOnScreen();
        });
    }
}

%ctor
{
    iAntsControlAbilityLog(@"[SBPasscode] 初始化自动解锁组件...");
    dlopen("/System/Library/PrivateFrameworks/UserNotificationsUIKit.framework/UserNotificationsUIKit", RTLD_LAZY);
    dlopen("/System/Library/PrivateFrameworks/SpringBoardUIServices.framework/SpringBoardUIServices", RTLD_LAZY);
    CFNotificationCenterAddObserver(CFNotificationCenterGetDarwinNotifyCenter(), NULL, screenDisplayStatus, CFSTR("com.apple.iokit.hid.displayStatus"), NULL, (CFNotificationSuspensionBehavior)0);
    CFNotificationCenterAddObserver(CFNotificationCenterGetDarwinNotifyCenter(), NULL, screenLockStatus, CFSTR("com.apple.springboard.lockstate"), NULL, (CFNotificationSuspensionBehavior)0);
    
    // 添加永不锁屏设置变更通知监听
    CFNotificationCenterAddObserver(CFNotificationCenterGetDarwinNotifyCenter(), NULL, preventAutoLockChanged, CFSTR("com.iants.preventAutoLock.changed"), NULL, (CFNotificationSuspensionBehavior)0);
    
    %init;
    
    // 初始化时根据设置决定是否解锁
    // 默认不在初始化时解锁，而是在收到锁屏事件时根据设置决定是否解锁
    if (shouldAutoUnlock()) {
        iAntsControlAbilityLog(@"[SBPasscode] 自动解锁功能已开启");
    } else {
        iAntsControlAbilityLog(@"[SBPasscode] 自动解锁功能已关闭");
    }
}

