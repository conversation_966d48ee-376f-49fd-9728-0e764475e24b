#include "Common.h"
#include "Config.h"
#import <sys/utsname.h>
#import <SpringBoard/SpringBoard.h>
#import <spawn.h>

/*
Get device model name
*/
NSString* getDeviceName()
{
    struct utsname systemInfo;
    uname(&systemInfo);

    return [NSString stringWithCString:systemInfo.machine
                                encoding:NSUTF8StringEncoding];
}

/*
round up number by multiple of another number
*/
int roundUp(int numToRound, int multiple)
{
    if (multiple == 0)
        return numToRound;

    int remainder = numToRound % multiple;
    if (remainder == 0)
        return numToRound;

    return numToRound + multiple - remainder;
}

/*
Check whether current device is an iPad
*/
Boolean isIpad()
{
    if ( UI_USER_INTERFACE_IDIOM() == UIUserInterfaceIdiomPad )
    {
        return YES;
    }
    return NO;
}

/*
generate a random integer between min and max.

ONLY POSITIVE NUMBER IS SUPPORTED!
*/
int getRandomNumberInt(int min, int max)
{
	min = abs(min);
	max = abs(max);

	if (max < min)
	{
		iAntsControlAbilityLog(@"Max is less than min in getRandomNumberInt(). max: %d, min: %d", max, min);
	}
	return arc4random_uniform(abs(max-min)) + min;
}

/*
generate a random float between min and max.

ONLY POSITIVE NUMBER IS SUPPORTED!
ONLY SUPPORTS TO UP TO 5 DIGIT.
*/
float getRandomNumberFloat(float min, float max)
{
	min = abs(min);
	max = abs(max);

	if (max < min)
	{
		iAntsControlAbilityLog(@"Max is less than min in getRandomNumberFloat(). max: %f, min: %f", max, min);
	}

	
	return getRandomNumberInt((int)(min*10000), (int)(max*10000))/10000.0f;
}

/**
Get document root of springboard
*/
NSString* getDocumentRoot()
{
    //NSArray *paths = NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, NSUserDomainMask, YES);
    return [NSString stringWithFormat:@"/var/mobile/Library/%s/" ,DOCUMENT_ROOT_FOLDER_NAME];
}

/**
Get scripts path
*/
NSString* getScriptsFolder()
{
    //NSArray *paths = NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, NSUserDomainMask, YES);
    return [NSString stringWithFormat:@"%@/%s/", getDocumentRoot(), SCRIPT_FOLDER_NAME];
}

/**
Get config dir
*/
NSString *getConfigFilePath()
{
	return [getDocumentRoot() stringByAppendingPathComponent:@CONFIG_FOLDER_NAME];
}

NSString *getCommonConfigFilePath()
{
    return [getConfigFilePath() stringByAppendingPathComponent:@COMMON_CONFIG_NAME];
}

void swapCGFloat(CGFloat *a, CGFloat *b)
{
	CGFloat temp = *a;
	*a = *b;
	*b = temp;
}

pid_t system2(const char * command, int * infp, int * outfp)
{
    iAntsControlAbilityLog(@"system2,command=:%s", command);
    int p_stdin[2];
    int p_stdout[2];
    pid_t pid;

    // 创建管道
    if (pipe(p_stdin) == -1) {
        NSLog(@"[error]: Failed to create stdin pipe.");
        return -1;
    }

    if (pipe(p_stdout) == -1) {
        NSLog(@"[error]: Failed to create stdout pipe.");
        close(p_stdin[0]);
        close(p_stdin[1]);
        return -1;
    }

    pid = fork();
    iAntsControlAbilityLog(@"system2, call fork() successful 1");

    if (pid < 0) {
        close(p_stdin[0]);
        close(p_stdin[1]);
        close(p_stdout[0]);
        close(p_stdout[1]);
        return pid;
    } else if (pid == 0) {
        close(p_stdin[1]);
        dup2(p_stdin[0], 0);
        close(p_stdout[0]);
        dup2(p_stdout[1], 1);
        dup2(::open("/dev/null", O_RDONLY), 2);
        /// Close all other descriptors for the safety sake.
        for (int i = 3; i < 4096; ++i)
            ::close(i);

        setsid();
        NSString *jbSh = [NSString stringWithFormat:@"%@bin/sh", getJbPath()];
        iAntsControlAbilityLog(@"system2, call execl %@ sh -c %s",jbSh, command);
        execl(jbSh.UTF8String, "sh", "-c", command, NULL);
        iAntsControlAbilityLog(@"system2, call execl successful 2");
        _exit(1);
    }

    close(p_stdin[0]);
    close(p_stdout[1]);

    // 如果调用方不需要输入/输出管道，则关闭它们
    if (infp == NULL) {
        close(p_stdin[1]);
    } else {
        *infp = p_stdin[1];
    }

    if (outfp == NULL) {
        close(p_stdout[0]);
    } else {
        *outfp = p_stdout[0];
    }

    return pid;
}


void executeZxCommand(NSString *args) {
    // NSString *command = [NSString stringWithFormat:@"sudo zxtouchb -e \"%@\"", args];
    NSString *command = [NSString stringWithFormat:@"zxtouchb -e \"%@\"", args];
    // NSString *command = [NSString stringWithFormat:@"%@", args];

    // iAntsControlAbilityLog(@"executing command: %@", command);
    system2([command UTF8String], NULL, NULL);
}

void execShellCommand(NSString *args) {
    // 过滤一下
    NSString *command = [NSString stringWithFormat:@"%@", args];

    // iAntsControlAbilityLog(@"executing command: %@", command);
    system2([command UTF8String], NULL, NULL);
}


NSString *execShellCommandWithOutput(NSString *args) {
    NSString *command = [NSString stringWithFormat:@"%@", args];
    iAntsControlAbilityLog(@"executing command with output: %@", command);

    int outfp;
    // 执行带有输出的命令
    pid_t pid = system2([command UTF8String], NULL, &outfp);
    if (pid == -1) {
        iAntsControlAbilityLog(@"failed to execute command.");
        return nil;
    }

    // 从管道中读取输出
    NSMutableString *output = [[NSMutableString alloc] init];
    char buffer[256];
    ssize_t bytesRead;

    while ((bytesRead = read(outfp, buffer, sizeof(buffer) - 1)) > 0) {
        buffer[bytesRead] = '\0';
        [output appendString:[NSString stringWithUTF8String:buffer]];
    }

    close(outfp);

    iAntsControlAbilityLog(@"command output: %@", output);
    return [output copy];
}

NSString *executeZxCommandWithOutput(NSString *args) {
    // NSString *command = [NSString stringWithFormat:@"sudo zxtouchb -e \"%@\"", args];
    NSString *command = [NSString stringWithFormat:@"zxtouchb -e \"%@\"", args];
    iAntsControlAbilityLog(@"executing command with output: %@", command);

    int outfp;
    // 执行带有输出的命令
    pid_t pid = system2([command UTF8String], NULL, &outfp);
    if (pid == -1) {
        iAntsControlAbilityLog(@"failed to execute command.");
        return nil;
    }

    // 从管道中读取输出
    NSMutableString *output = [[NSMutableString alloc] init];
    char buffer[256];
    ssize_t bytesRead;

    while ((bytesRead = read(outfp, buffer, sizeof(buffer) - 1)) > 0) {
        buffer[bytesRead] = '\0';
        [output appendString:[NSString stringWithUTF8String:buffer]];
    }

    close(outfp);

    iAntsControlAbilityLog(@"command output: %@", output);
    return [output copy];
}


BOOL checkIfProcessRunning(NSString *processKeyword) {
    int outfp;
    // 构建检测命令
    NSString *command = [NSString stringWithFormat:@"ps -e | grep %@ | grep -v grep", processKeyword];
    // iAntsControlAbilityLog(@"checkIfProcessRunning command: %@",command);
    
    // 执行命令并将输出写入管道
    system2([command UTF8String], NULL, &outfp);

    // 从管道中读取输出
    char buffer[256];
    ssize_t bytesRead = read(outfp, buffer, sizeof(buffer) - 1);
    close(outfp);

    // 如果读取到内容，说明进程存在
    if (bytesRead > 0) {
        buffer[bytesRead] = '\0';
        iAntsControlAbilityLog(@"检测到运行中的 %@ 进程: %s", processKeyword, buffer);
        // iAntsControlAbilityLog(@"完整的输出内容为：%s", buffer);  // 打印完整输出内容
        return YES;
    } else {
        iAntsControlAbilityLog(@"未检测到 %@ 进程", processKeyword);
        return NO;
    }
}


NSString *getJbPath() {
    #if ROOTHIDE
    return [NSString stringWithFormat:@"%s", jbroot("/")];
    #elif ROOTLESS
    return @"/var/jb/";
    #elif ROOTFULL
    return @"/";
    #elif NOROOT
    return @"/";
    #else
    return @"";
    #endif
}

NSString *systeAlertHandleConfigFilePath = [NSString stringWithFormat:@"%@var/mobile/Library/Preferences/com.data.iantscore/config/systemAlertHandleConfig.json", getJbPath()];
NSDictionary *getSysytemAlertHandleConfig() {

    iAntsControlAbilityLog(@"getSysytemAlertHandleConfig config path: %@", systeAlertHandleConfigFilePath);
    if (![[NSFileManager defaultManager] fileExistsAtPath:systeAlertHandleConfigFilePath]) {
        iAntsControlAbilityLog(@"getSysytemAlertHandleConfig config not exists");
        return nil;
    }
    NSData *jsonData = [NSData dataWithContentsOfFile:systeAlertHandleConfigFilePath];
    if (!jsonData) {
        iAntsControlAbilityLog(@"Failed to load JSON file. %@", systeAlertHandleConfigFilePath);
        return nil;
    }
    
    NSError *error = nil;
    NSDictionary *systemAlertHandleConfig = [NSJSONSerialization JSONObjectWithData:jsonData options:0 error:&error];
    if (error) {
        iAntsControlAbilityLog(@"Failed to parse JSON file. %@", error.localizedDescription);
        return nil;
    }
    return systemAlertHandleConfig;
}

void WriteDebugLog(NSString *fmt, ...) {
#if IAntsTouchAbilityDEBUG
    // 1. 组装日志内容（支持可变参数）
    va_list args;
    va_start(args, fmt);
    NSString *msg = [[NSString alloc] initWithFormat:fmt arguments:args];
    va_end(args);

    // 2. 获取当前日期，格式 yyyyMMdd
    NSDateFormatter *dateFmt = [[NSDateFormatter alloc] init];
    [dateFmt setDateFormat:@"yyyyMMdd"];
    NSString *dateStr = [dateFmt stringFromDate:[NSDate date]];
    NSString *logFile = [NSString stringWithFormat:@"/tmp/test_debug_%@.log", dateStr];

    // 3. 检查文件是否存在
    NSFileManager *fm = [NSFileManager defaultManager];
    BOOL isDir = NO;
    BOOL exists = [fm fileExistsAtPath:logFile isDirectory:&isDir];

    // 4. 如果文件不存在，新建（自动清空）
    if (!exists) {
        [fm createFileAtPath:logFile contents:nil attributes:nil];
    }

    // 5. 打开文件
    NSFileHandle *handle = [NSFileHandle fileHandleForWritingAtPath:logFile];
    if (!handle) return;

    // 6. 时间戳
    NSDateFormatter *tsFmt = [[NSDateFormatter alloc] init];
    [tsFmt setDateFormat:@"yyyy-MM-dd HH:mm:ss"];
    NSString *timestamp = [tsFmt stringFromDate:[NSDate date]];
    NSString *line = [NSString stringWithFormat:@"[%@] %@\n", timestamp, msg];

    // 7. 写入文件
    [handle seekToEndOfFile];
    [handle writeData:[line dataUsingEncoding:NSUTF8StringEncoding]];
    [handle closeFile];
#endif
}