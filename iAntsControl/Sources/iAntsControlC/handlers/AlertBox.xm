#include "AlertBox.h"
#import "../server/SocketServer.h"
#import "../Common.h"
#import "LogC.h"

void showAlertBoxFromRawData(UInt8 *eventData, NSError **error)
{
    NSString *alertData = [NSString stringWithUTF8String:(char*)eventData];
    NSArray *alertDataArray = [alertData componentsSeparatedByString:@";;"];
    if ([alertDataArray count] < 3)
    {
        *error = [NSError errorWithDomain:@"com.data.iantscore.plugin.touchability" code:999 userInfo:@{NSLocalizedDescriptionKey:@"-1;;Unable to show alert box. The socket format should be title;;content;;duration.\r\n"}];
        return;
    }
    if ([alertDataArray[2] intValue] == 0)
    {
        *error = [NSError errorWithDomain:@"com.data.iantscore.plugin.touchability" code:999 userInfo:@{NSLocalizedDescriptionKey:@"-1;;Duration should be a integer that is greater than 0\r\n"}];
        return;
    }
    showAlertBox(alertDataArray[0], alertDataArray[1], [alertDataArray[2] intValue]);
}

void showAlertBox(NSString* title, NSString* content, int dismissTime)
{
    LOGD(@"[AlertBox] 执行弹窗: %@, %@, %d", title, content, dismissTime);
    NSMutableDictionary* dict = [NSMutableDictionary dictionary];
    [dict setObject: title forKey: (__bridge NSString*)kCFUserNotificationAlertHeaderKey];
    [dict setObject: content forKey: (__bridge NSString*)kCFUserNotificationAlertMessageKey];
    [dict setObject: @"OK" forKey:(__bridge NSString*)kCFUserNotificationDefaultButtonTitleKey];
    
    SInt32 error = 0;
    CFUserNotificationRef alert = CFUserNotificationCreate(NULL, 0, kCFUserNotificationPlainAlertLevel, &error, (__bridge CFDictionaryRef)dict);

    CFOptionFlags response;
    
     if((error) || (CFUserNotificationReceiveResponse(alert, dismissTime, &response))) {
        iAntsControlAbilityLog(@"alert error or no user response after %d seconds for title: %@. Content %@", dismissTime, title, content);
     }
    
    /*
    else if((response & 0x3) == kCFUserNotificationAlternateResponse) {
        NSLog(@"cancel");
    } else if((response & 0x3) == kCFUserNotificationDefaultResponse) {
        NSLog(@"view");
    }
    */

    CFRelease(alert);
    alert = nil;
}

void showAlertBoxAsync(NSString* title, NSString* content, int dismissTime)
{
    LOGI(@"[AlertBox] 异步执行弹窗: %@, %@, %d", title, content, dismissTime);
    
    // 在后台线程异步执行弹窗，避免阻塞WebSocket消息处理线程
    dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
        NSMutableDictionary* dict = [NSMutableDictionary dictionary];
        [dict setObject: title forKey: (__bridge NSString*)kCFUserNotificationAlertHeaderKey];
        [dict setObject: content forKey: (__bridge NSString*)kCFUserNotificationAlertMessageKey];
        [dict setObject: @"OK" forKey:(__bridge NSString*)kCFUserNotificationDefaultButtonTitleKey];
        
        SInt32 error = 0;
        CFUserNotificationRef alert = CFUserNotificationCreate(NULL, 0, kCFUserNotificationPlainAlertLevel, &error, (__bridge CFDictionaryRef)dict);

        if (error) {
            LOGE(@"[AlertBox] 创建弹窗失败，错误代码: %d", error);
            return;
        }

        CFOptionFlags response;
        
        // 这个阻塞调用现在在后台线程中，不会影响WebSocket连接
        if (CFUserNotificationReceiveResponse(alert, dismissTime, &response)) {
            LOGI(@"[AlertBox] 弹窗超时或用户未响应，标题: %@, 内容: %@, 超时时间: %d秒", title, content, dismissTime);
        } else {
            LOGI(@"[AlertBox] 用户响应了弹窗，响应码: %lu", (unsigned long)response);
        }
        
        CFRelease(alert);
        LOGI(@"[AlertBox] 异步弹窗处理完成");
    });
}

void showAdvancedAlertBox(
    NSString* title,
    NSString* content,
    NSString* defaultButtonTitle,
    NSString* alternateButtonTitle,
    NSString* otherButtonTitle,
    NSArray<NSString*>* checkBoxTitles,
    NSArray<NSString*>* textFieldTitles,
    NSArray<NSString*>* textFieldValues,
    NSArray<NSString*>* popUpTitles,
    NSNumber* popUpSelection,
    NSNumber* progressValue,
    NSURL* iconURL,
    int dismissTime
) {
    NSMutableDictionary* dict = [NSMutableDictionary dictionary];
    if (title) [dict setObject:title forKey:(__bridge NSString*)kCFUserNotificationAlertHeaderKey];
    if (content) [dict setObject:content forKey:(__bridge NSString*)kCFUserNotificationAlertMessageKey];
    if (defaultButtonTitle) [dict setObject:defaultButtonTitle forKey:(__bridge NSString*)kCFUserNotificationDefaultButtonTitleKey];
    if (alternateButtonTitle) [dict setObject:alternateButtonTitle forKey:(__bridge NSString*)kCFUserNotificationAlternateButtonTitleKey];
    if (otherButtonTitle) [dict setObject:otherButtonTitle forKey:(__bridge NSString*)kCFUserNotificationOtherButtonTitleKey];
    if (checkBoxTitles) [dict setObject:checkBoxTitles forKey:(__bridge NSString*)kCFUserNotificationCheckBoxTitlesKey];
    if (textFieldTitles) [dict setObject:textFieldTitles forKey:(__bridge NSString*)kCFUserNotificationTextFieldTitlesKey];
    if (textFieldValues) [dict setObject:textFieldValues forKey:(__bridge NSString*)kCFUserNotificationTextFieldValuesKey];
    if (popUpTitles) [dict setObject:popUpTitles forKey:(__bridge NSString*)kCFUserNotificationPopUpTitlesKey];
    // popUpSelection 在 iOS 上不可用，跳过
    // if (popUpSelection) [dict setObject:popUpSelection forKey:(__bridge NSString*)kCFUserNotificationPopUpSelectionKey];
    if (progressValue) [dict setObject:progressValue forKey:(__bridge NSString*)kCFUserNotificationProgressIndicatorValueKey];
    if (iconURL) [dict setObject:iconURL forKey:(__bridge NSString*)kCFUserNotificationIconURLKey];

    SInt32 error = 0;
    CFUserNotificationRef alert = CFUserNotificationCreate(NULL, 0, kCFUserNotificationPlainAlertLevel, &error, (__bridge CFDictionaryRef)dict);

    CFOptionFlags response;
    if ((error) || (CFUserNotificationReceiveResponse(alert, dismissTime, &response))) {
        NSLog(@"alert error or no user response after %d seconds for title: %@. Content %@", dismissTime, title, content);
    }
    CFRelease(alert);
}
