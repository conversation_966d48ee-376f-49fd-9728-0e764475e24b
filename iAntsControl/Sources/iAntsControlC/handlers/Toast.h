#ifndef TOAST_H
#define TOAST_H

#ifdef __cplusplus
extern "C" {
#endif

#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>
#include <stdint.h>
#import <CoreFoundation/CoreFoundation.h>

void showToastFromRawData(UInt8 *eventData, NSError **error);

@interface Toast : NSObject
+ (void) showToastWithContent:(NSString*)content type:(int)type duration:(float)duration position:(int)position fontSize:(int)afontSize; // positon: 0 top 1 bottom 2 left(not supported) 3 right (ns)
+ (void) hideToast;
- (void) show;
- (void) setContent:(NSString*)content;
- (void) setBackgroundColor:(UIColor*)color;
- (void) setDuration:(int)d;

@end

#ifdef __cplusplus
}
#endif

#endif