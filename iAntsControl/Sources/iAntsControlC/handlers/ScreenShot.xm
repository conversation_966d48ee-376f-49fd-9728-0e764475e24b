#import "../IAntsOCR/IAntsOCRManager.h"
#import "../Common.h"


NSString *getScreenShotFromRawData(UInt8* eventData, NSError **error, bool *success)
{
    return [IAntsOCRManager privateApiScreenshot:DTOCR content:nil error:error success:success];
}


NSString *getScreenShot4ShowUIFromRawData(UInt8* eventData, NSError **error, bool *success)
{
    NSArray *data = [[NSString stringWithUTF8String:(const char *)eventData] componentsSeparatedByString:@";;"];
    if (!data || data.count < 1) {
        if (error) {
            *error = [NSError errorWithDomain:@"getScreenShot4ShowUIFromRawData" code:1030 userInfo:@{NSLocalizedDescriptionKey:[NSString stringWithFormat:@"data is empty, %@", data]}];
        }
        return nil;
    }
    NSString *prompt = data[0];
    iAntsControlAbilityLog(@"prompt: %@", prompt);
    return [IAntsOCRManager privateApiScreenshot:SHOWUI content:@{@"prompt" : prompt} error:error success:success];
}


NSString *getScreenShot4SaveFromRawData(UInt8* eventData, NSError **error, bool *success) {
    NSArray *data = [[NSString stringWithUTF8String:(const char *)eventData] componentsSeparatedByString:@";;"];
    if (!data || data.count < 1) {
        if (error) {
            *error = [NSError errorWithDomain:@"getScreenShot4SaveFromRawData" code:1030 userInfo:@{NSLocalizedDescriptionKey:[NSString stringWithFormat:@"data is empty, %@", data]}];
        }
        return nil;
    }
    NSString *fileName = data[0];
    iAntsControlAbilityLog(@"fileName: %@", fileName);
    return [IAntsOCRManager privateApiScreenshot:SAVE content:@{@"fileName" : fileName} error:error success:success];
}

NSString *getScreenShot4StateFromRawData(UInt8* eventData, NSError **error, bool *success) {
    NSArray *data = [[NSString stringWithUTF8String:(const char *)eventData] componentsSeparatedByString:@";;"];
    if (!data || data.count < 1) {
        if (error) {
            *error = [NSError errorWithDomain:@"getScreenShot4StateFromRawData" code:1030 userInfo:@{NSLocalizedDescriptionKey:[NSString stringWithFormat:@"data is empty, %@", data]}];
        }
        return nil;
    }
    NSString *brand = data[0];
    iAntsControlAbilityLog(@"brand: %@", brand);
    return [IAntsOCRManager privateApiScreenshot:STATE content:@{@"brand" : brand} error:error success:success];
}

NSString *getScreenShot4CaptchaHandleFromRawData(UInt8* eventData, NSError **error, bool *success) {
    NSArray *data = [[NSString stringWithUTF8String:(const char *)eventData] componentsSeparatedByString:@";;"];
    if (!data || data.count < 2) {
        if (error) {
            *error = [NSError errorWithDomain:@"getScreenShot4StateFromRawData" code:1030 userInfo:@{NSLocalizedDescriptionKey:[NSString stringWithFormat:@"data is empty, %@", data]}];
        }
        return nil;
    }
    NSString *brand = data[0];
    NSString *type = data[1];
    iAntsControlAbilityLog(@"brand: %@ type: %@", brand, type);
    return [IAntsOCRManager privateApiScreenshot:CAPTCHA content:@{@"brand" : brand, @"type" : type} error:error success:success];
}