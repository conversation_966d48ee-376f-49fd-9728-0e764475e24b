syntax = "proto3";

package iAnts;

// -----------------------------------------------------------------------------
// 公共消息类型（所有组件都需要的）
// -----------------------------------------------------------------------------
enum CommonMessageType {
  HEARTBEAT = 0;   // 心跳消息
  ERROR     = 999; // 错误消息
}

// -----------------------------------------------------------------------------
// 公共消息体
// -----------------------------------------------------------------------------
message Heartbeat {
  string version = 1;  // 发送者的版本号
}

message ErrorMessage {
  string text = 1;
}
