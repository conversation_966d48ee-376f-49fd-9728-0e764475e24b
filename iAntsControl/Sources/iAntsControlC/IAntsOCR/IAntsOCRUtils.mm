//
//  IAntsORCUtils.m
//  touchtest
//
//  Created by mazhixiang on 2024/12/10.
//

#import "IAntsOCRUtils.h"
#import <UIKit/UIKit.h>
#import "../Common.h"

@implementation IAntsOCRUtils
+ (NSArray *)transferImageElementCoordinates:(NSArray<NSArray<NSNumber *> *> *)box imageSize:(CGSize)imageSize  {
    CGRect bounds = [UIScreen mainScreen].bounds;
    CGFloat width = bounds.size.width;
    CGFloat height = bounds.size.height;
    CGFloat scale = [UIScreen mainScreen].scale;
    
    CGFloat w = box[1][0].floatValue - box[0][0].floatValue;
    CGFloat l = box[2][1].floatValue - box[0][1].floatValue;

    CGFloat x_proportion = @(box[0][0].floatValue + w / 2).floatValue / imageSize.width;
    CGFloat y_proportion = @(box[0][1].floatValue + l / 2).floatValue / imageSize.height;
    
    CGFloat nx = x_proportion * width * scale;
    CGFloat ny = y_proportion * height * scale;

    NSArray *newCoord = @[@(nx), @(ny)];
    return newCoord;
}
+ (NSDictionary *)transferImageElements:(NSArray *)content imageSize:(CGSize)imageSize {
    NSMutableDictionary *newDict = [NSMutableDictionary new];
    for (NSDictionary *v in content) {
        NSArray *coord = [self transferImageElementCoordinates:v[@"box"] imageSize:imageSize];
        [newDict setObject:coord forKey:v[@"text"]];
    }
    return [NSDictionary dictionaryWithDictionary:newDict];
}

+ (CGSize)getImageSize4Base64Str:(NSString *)base64String {
    NSData *data = [[NSData alloc] initWithBase64EncodedString:base64String options:0];
    // 使用 NSData 创建 UIImage
    UIImage *image = [UIImage imageWithData:data];
    // 获取 UIImage 的尺寸
    CGSize imageSize = image.size;
    iAntsControlAbilityLog(@"Image Size: Width = %.2f, Height = %.2f", imageSize.width, imageSize.height);
    return imageSize;
}

@end
