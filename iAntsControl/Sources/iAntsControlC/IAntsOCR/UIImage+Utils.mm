//
//  UIImage+Utils.m
//  touchtest
//
//  Created by mazhixiang on 2024/12/10.
//

#import "UIImage+Utils.h"
#import "../Common.h"
@implementation UIImage (Utils)
- (NSString *)base64String {
    iAntsControlAbilityLog(@"image %@", self);
    NSData *imageData = UIImagePNGRepresentation(self);
    iAntsControlAbilityLog(@"imageData %@", imageData);
    NSString *base64String = [imageData base64EncodedStringWithOptions:NSDataBase64EncodingEndLineWithCarriageReturn];
#ifdef DEBUG
    NSData *data = [[NSData alloc] initWithBase64EncodedString:base64String options:0];
    // 使用 NSData 创建 UIImage
    UIImage *image = [UIImage imageWithData:data];
    // 获取 UIImage 的尺寸
    CGSize imageSize = image.size;
    iAntsControlAbilityLog(@"Image Size: Width = %.2f, Height = %.2f", imageSize.width, imageSize.height);
#endif
    return base64String;
}
@end
